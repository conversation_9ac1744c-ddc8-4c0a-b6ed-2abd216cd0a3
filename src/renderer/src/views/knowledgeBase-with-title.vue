<template>
  <div class="knowledge-container">
    <!-- 🎨 现代化标题栏 -->
    <div class="knowledge-header">
      <div class="header-content">
        <div class="title-section">
          <div class="title-icon">
            <el-icon class="icon-gradient"><Collection /></el-icon>
          </div>
          <div class="title-info">
            <h1 class="page-title gradient-text">知识库</h1>
            <span class="page-subtitle">Knowledge Base Management</span>
          </div>
        </div>
        
        <!-- 🎯 操作按钮区域 -->
        <div class="header-actions">
          <button class="action-btn refresh-btn" @click="refreshKnowledge">
            <el-icon><Refresh /></el-icon>
            <span>刷新</span>
          </button>
          <button class="action-btn fullscreen-btn" @click="toggleFullscreen">
            <el-icon><FullScreen /></el-icon>
            <span>全屏</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 🎯 知识库内容区域 -->
    <div class="knowledge-content">
      <iframe 
        :src="url" 
        frameborder="0" 
        class="knowledge-iframe"
        :class="{ 'fullscreen-mode': isFullscreen }"
      ></iframe>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { Collection, Refresh, FullScreen } from '@element-plus/icons-vue'

const url = ref("")
const isFullscreen = ref(false)

onMounted(() => {
  url.value = import.meta.env.VITE_APP_CONSOLE_URL
})

// 刷新知识库
const refreshKnowledge = () => {
  const iframe = document.querySelector('.knowledge-iframe') as HTMLIFrameElement
  if (iframe) {
    iframe.src = iframe.src
  }
}

// 切换全屏模式
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}
</script>

<style lang="scss" scoped>
/* 🎨 知识库容器 */
.knowledge-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-xl);
}

/* 🎯 现代化标题栏 */
.knowledge-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md) var(--spacing-lg);
  position: relative;
  z-index: 10;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

/* 🏷️ 标题区域 */
.title-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);

  .title-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius-md);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);

    .icon-gradient {
      font-size: 24px;
      color: white;
    }
  }

  .title-info {
    .page-title {
      font-size: 28px;
      font-weight: 700;
      margin: 0 0 4px 0;
      background: var(--primary-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .page-subtitle {
      font-size: 14px;
      color: var(--text-secondary);
      font-weight: 500;
    }
  }
}

/* 🎯 操作按钮区域 */
.header-actions {
  display: flex;
  gap: var(--spacing-sm);

  .action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius-md);
    background: rgba(255, 255, 255, 0.8);
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s var(--ease-out-cubic);
    box-shadow: var(--shadow-sm);

    &:hover {
      background: var(--primary-gradient);
      color: white;
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

/* 🎯 知识库内容区域 */
.knowledge-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  padding: var(--spacing-md);
  background: var(--bg-secondary);
}

/* 🎨 知识库iframe */
.knowledge-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-lg);
  background: white;
  transition: all 0.3s var(--ease-out-cubic);

  /* 🎯 现代化边框效果 */
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: var(--border-radius-md);
    background: var(--primary-gradient);
    z-index: -1;
    opacity: 0.6;
  }

  /* 🎯 全屏模式 */
  &.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    border-radius: 0;
  }
}

/* 🎨 渐变文字效果 */
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 🎯 响应式设计 */
@media (max-width: 768px) {
  .knowledge-header {
    padding: var(--spacing-sm) var(--spacing-md);
    
    .header-content {
      flex-direction: column;
      gap: var(--spacing-md);
      align-items: flex-start;
    }
  }

  .title-section {
    .title-info .page-title {
      font-size: 24px;
    }
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
