// src/store/configStore.ts
import fs from 'fs';
import path from 'path';
import { AppConfig } from '../types/config';

// 获取应用程序的根目录路径
const rootPath = process.env.NODE_ENV === 'development'
  ? path.join(__dirname, '..')
  : path.join(process.resourcesPath, 'app.asar.unpacked');

const configPath = path.join(rootPath, 'config.json');

const defaultConfig: AppConfig = {
  apiKey: '',
  modelName: '',
  debug: false,
};

let config: AppConfig = loadConfig();

function loadConfig(): AppConfig {
  try {
    if (fs.existsSync(configPath)) {
      return JSON.parse(fs.readFileSync(configPath, 'utf-8'));
    }
  } catch (error) {
    console.error('Failed to load config:', error);
  }
  return { ...defaultConfig };
}

function saveConfig(): void {
  try {
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf-8');
  } catch (error) {
    console.error('Failed to save config:', error);
  }
}

export const getConfig = (): AppConfig => config;

export const setConfig = (newConfig: Partial<AppConfig>): void => {
  config = { ...config, ...newConfig };
  saveConfig(); // 保存配置到磁盘
};

export const resetConfig = (): void => {
  config = { ...defaultConfig };
  saveConfig(); // 重置后也保存
};