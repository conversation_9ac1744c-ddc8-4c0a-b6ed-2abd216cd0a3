/* 🎨 现代化设计系统 - 科技感AI客服界面 */
:root {
  /* 🎯 主色调 - 科技蓝渐变 */
  --primary-color: #667eea;
  --primary-light: #764ba2;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-gradient-hover: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);

  /* 🌈 辅助色彩 */
  --secondary-color: #f093fb;
  --accent-color: #4facfe;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;

  /* 🎨 背景色系 */
  --bg-primary: #f8fafc;
  --bg-secondary: #f1f5f9;
  --bg-tertiary: #e2e8f0;
  --bg-glass: rgba(255, 255, 255, 0.25);
  --bg-glass-hover: rgba(255, 255, 255, 0.35);

  /* 📝 文字色系 */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --text-inverse: #ffffff;

  /* 🔲 边框和阴影 */
  --border-color: #e2e8f0;
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 20px;
  --border-radius-2xl: 25px;

  /* ✨ 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* 🌟 Neumorphism 效果 */
  --neumorphism-light: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff;
  --neumorphism-inset: inset 8px 8px 16px #d1d9e6, inset -8px -8px 16px #ffffff;
  --neumorphism-hover: 12px 12px 24px #d1d9e6, -12px -12px 24px #ffffff;

  /* 🎭 动画曲线 */
  --ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
  --ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
  --ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* 📏 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;
}

/* 🔄 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #64748b;
    --border-color: #334155;
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-weight: normal;
  user-select: none;
}

/* 📜 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border-radius: var(--border-radius-sm);
  transition: all 0.3s var(--ease-out-cubic);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-gradient-hover);
  transform: scale(1.1);
}

ul {
  list-style: none;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  font-size: 16px;
  line-height: 1.6;
  background: var(--bg-primary);
  font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-primary);
}

#app {
  border-radius: var(--border-radius-md);
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.select-view-box .el-message-box__message {
  width: 100%;
}

.header-title {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--el-border-color);
}

.header-title h1 {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}
