/* 🎨 现代化组件样式库 - AI客服专用 */

/* 🔘 现代化按钮系统 */
.modern-btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s var(--ease-out-cubic);
  overflow: hidden;
  backdrop-filter: blur(10px);
  user-select: none;
}

.modern-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-btn:hover::before {
  left: 100%;
}

.modern-btn-primary {
  background: var(--primary-gradient);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.modern-btn-primary:hover {
  background: var(--primary-gradient-hover);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.modern-btn-secondary {
  background: var(--bg-glass);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.modern-btn-secondary:hover {
  background: var(--bg-glass-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.modern-btn-glass {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.modern-btn-glass:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 📱 现代化卡片系统 */
.modern-card {
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(10px);
  transition: all 0.3s var(--ease-out-cubic);
  position: relative;
  overflow: hidden;
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s var(--ease-out-cubic);
}

.modern-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.modern-card:hover::before {
  opacity: 1;
}

.modern-card-neumorphism {
  background: var(--bg-primary);
  border: none;
  box-shadow: var(--neumorphism-light);
}

.modern-card-neumorphism:hover {
  box-shadow: var(--neumorphism-hover);
}

/* 🎯 现代化输入框 */
.modern-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background: var(--bg-glass);
  color: var(--text-primary);
  font-size: 14px;
  transition: all 0.3s var(--ease-out-cubic);
  backdrop-filter: blur(10px);
}

.modern-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 0.9);
}

.modern-input::placeholder {
  color: var(--text-tertiary);
}

/* 🌟 发光效果 */
.glow-effect {
  position: relative;
}

.glow-effect::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--primary-gradient);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s var(--ease-out-cubic);
}

.glow-effect:hover::after {
  opacity: 0.3;
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

/* 🎭 3D悬浮效果 */
.floating-3d {
  transform-style: preserve-3d;
  transition: transform 0.3s var(--ease-out-cubic);
}

.floating-3d:hover {
  transform: perspective(1000px) rotateX(5deg) rotateY(5deg) translateZ(10px);
}

/* 📊 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 6px 12px;
  border-radius: var(--border-radius-xl);
  font-size: 12px;
  font-weight: 500;
}

.status-online {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-offline {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-busy {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 🎨 渐变文字 */
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

/* 🔄 加载动画 */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 🎪 页面过渡动画 */
.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.3s var(--ease-out-cubic);
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 📱 响应式工具类 */
.glass-morphism {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hover-lift {
  transition: transform 0.3s var(--ease-out-cubic);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

/* 🎯 工具类 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

.overflow-hidden {
  overflow: hidden;
}

.text-center {
  text-align: center;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}
