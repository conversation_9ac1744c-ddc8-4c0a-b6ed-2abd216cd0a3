<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from 'vue-router';

const isShowMessage = ref(true)

const router = useRouter();
const route = useRoute();

// 当前路由路径
const currentRoute = computed(() => route.path);

function showMessage(message: string) {
  const path = '/main/index/home';
  window.api.selectView("-1");
  if (!currentRoute.value.includes('/main/index')) {
    router.push(path);
  }

  if (message.trim().length === 0) {
    message = 'Ai客服处理不过来，请接管！'
  }
  if (isShowMessage.value) {
    isShowMessage.value = false
    ElMessageBox.alert(message, '警告', {
      confirmButtonText: '确认',
      callback: () => {
        isShowMessage.value = true
      }
    })
  }
}

function palyAudio() {
  if (!isShowMessage.value) {
    const audio = new Audio('../src/assets/warning.wav')
    audio.play()
  }
  setTimeout(() => {
    palyAudio()
  }, 1000);
}

palyAudio()


window.api.warning((message: string) => {
  showMessage(message)
})
</script>

<template>
  <router-view></router-view>
</template>
