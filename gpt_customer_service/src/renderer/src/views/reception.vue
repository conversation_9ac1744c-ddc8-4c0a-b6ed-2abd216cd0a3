<template>
  <div class="reception modern-reception">
    <!-- 🌟 背景装饰 -->
    <div class="bg-decoration">
      <div class="bg-gradient"></div>
      <div class="bg-pattern"></div>
    </div>

    <div class="main modern-main">
      <!-- 🎯 左侧控制面板 -->
      <div class="left-wrapper modern-sidebar">
        <div class="sidebar-header">
          <h2 class="sidebar-title">
            <el-icon class="title-icon"><Monitor /></el-icon>
            接待中心
          </h2>
        </div>

        <el-tabs v-model="leftTabsActiveName" class="modern-tabs" @tab-change="getViews">
          <el-tab-pane name="first">
            <template #label>
              <div class="modern-tab-label">
                <span class="tab-text">值班中</span>
                <span class="tab-badge online-badge">{{ views.filter((view) => view.loginState == 1).length }}</span>
              </div>
            </template>
            <el-scrollbar class="tab-scrollbar">
              <div class="view-container">
                <transition-group name="view-list" tag="div" class="view-list">
                  <div
                    v-for="view in views.filter((view) => view.loginState == 1)"
                    :key="view.viewId"
                    class="view-item modern-card hover-lift"
                    @click="selectView(view)"
                  >
                    <div class="view-avatar">
                      <img class="view-image" :src="shopIcon[view.Shop.name]" :alt="view.Shop.name" />
                      <div class="online-indicator"></div>
                    </div>
                    <div class="view-content">
                      <div class="view-header">
                        <span class="view-name">{{ view.name }}</span>
                        <div class="view-status">
                          <span class="platform-tag">{{ view.Shop.name }}</span>
                        </div>
                      </div>
                      <div class="view-controls">
                        <el-switch
                          :disabled="view.loginState == 0"
                          v-model="view.isDuty"
                          inline-prompt
                          :active-value="1"
                          :inactive-value="0"
                          class="duty-switch"
                          active-text="值班"
                          inactive-text="休息"
                          @change="changeDuty(view)"
                        />
                      </div>
                    </div>
                  </div>
                </transition-group>
                <el-empty v-if="views.filter((view) => view.loginState == 1).length === 0"
                  description="暂无值班客服"
                  class="modern-empty"
                >
                  <template #image>
                    <el-icon class="empty-icon"><UserFilled /></el-icon>
                  </template>
                </el-empty>
              </div>
            </el-scrollbar>
          </el-tab-pane>

          <el-tab-pane name="second">
            <template #label>
              <div class="modern-tab-label">
                <span class="tab-text">未登录</span>
                <span class="tab-badge offline-badge">{{ views.filter((view) => view.loginState == 0).length }}</span>
              </div>
            </template>
            <el-scrollbar class="tab-scrollbar">
              <div class="view-container">
                <transition-group name="view-list" tag="div" class="view-list">
                  <div
                    v-for="view in views.filter((view) => view.loginState == 0)"
                    :key="view.viewId"
                    class="view-item modern-card hover-lift offline-view"
                    @click="selectView(view)"
                  >
                    <div class="view-avatar">
                      <img class="view-image" :src="shopIcon[view.Shop.name]" :alt="view.Shop.name" />
                      <div class="offline-indicator"></div>
                    </div>
                    <div class="view-content">
                      <div class="view-header">
                        <span class="view-name">{{ view.Shop.name }}</span>
                        <div class="view-status">
                          <span class="platform-tag offline">未登录</span>
                        </div>
                      </div>
                    </div>
                    <el-button
                      type="danger"
                      size="small"
                      circle
                      class="close-btn"
                      @click.stop="closeView(view.viewId)"
                    >
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                </transition-group>
                <el-empty v-if="views.filter((view) => view.loginState == 0).length === 0"
                  description="所有平台已登录"
                  class="modern-empty"
                >
                  <template #image>
                    <el-icon class="empty-icon"><SuccessFilled /></el-icon>
                  </template>
                </el-empty>
              </div>
            </el-scrollbar>
          </el-tab-pane>
        </el-tabs>

        <!-- 🏪 平台选择面板 -->
        <div class="shop-wrapper modern-shop-panel" v-if="showShopList">
          <div class="shop-header">
            <div class="header-content">
              <el-icon class="header-icon"><Shop /></el-icon>
              <h3 class="header-title">选择平台</h3>
            </div>
            <el-button size="small" circle @click="showShopList = false">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          <div class="shop-content">
            <transition-group name="shop-grid" tag="div" class="shop-grid">
              <div
                v-for="shop in shopList"
                :key="shop.id"
                class="shop-card modern-card hover-lift"
                @click="openNewView(shop.id)"
              >
                <div class="shop-icon">
                  <el-image :src="shop.icon" class="platform-image">
                    <template #error>
                      <div class="image-placeholder">
                        <el-icon><Picture /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>
                <div class="shop-info">
                  <span class="shop-name">{{ shop.name }}</span>
                  <span class="shop-desc">点击连接</span>
                </div>
              </div>
            </transition-group>
          </div>
        </div>
      </div>

      <!-- 🖥️ 右侧内容区域 -->
      <div class="right-wrapper modern-content">
        <div class="content-header">
          <div class="header-info">
            <h3 class="content-title">客服工作台</h3>
            <p class="content-desc">在此处处理客户咨询和消息</p>
          </div>
        </div>
        <div class="content-body">
          <div id="embed-container" class="embed-container"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { h, ref, onMounted } from 'vue';
import { ElMessageBox, ElSelect, ElOption, ElMessage } from 'element-plus'
import {
  Monitor,
  UserFilled,
  Warning,
  Close,
  Shop,
  Picture,
  SuccessFilled
} from '@element-plus/icons-vue'
import doudian from '@renderer/assets/icon/doudian.png'
import kuaishouxiaodian from '@renderer/assets/icon/kuaishouxiaodian.png'
import qianniu from '@renderer/assets/icon/qianniu.png'
import weixinxiaodian from '@renderer/assets/icon/weixinxiaodian.png'
import xiaohongshu from '@renderer/assets/icon/xiaohongshu.png'
import pingduoduo from '@renderer/assets/icon/pingduoduo.png'

const shopIcon = {
  "抖店飞鸽": doudian,
  "千牛天猫": qianniu,
  "小红书千帆": xiaohongshu,
  "拼多多": pingduoduo,
  "微信小店": weixinxiaodian,
  "快手小店": kuaishouxiaodian,
}

// 左侧菜单
const leftTabsActiveName = ref('first');

interface knowledgeBase {
  id: Number,
  kb_name: String,
  kb_info: String,
  vs_type: String,
  embed_model: String,
  file_count: Number,
  create_time: String
}

// 知识库列表
const knowledgeBases = ref(Array<knowledgeBase>());
const selectedKnowledgeBase = ref("");

// 获取知识库列表
function getKnowledgeBases() {
  window.api.getKnowledgeBasesList().then(res => {
    knowledgeBases.value = res.data;
  })
}

// 打开新窗口
function openNewView(id: string) {
  window.api.selectView("-1")
  if (knowledgeBases.value.length == 0) {
    getKnowledgeBases()
  }
  console.log(id);

  // ElMessageBox({
  //   title: '请选择知识库',
  //   customClass: 'select-view-box',
  //   showCancelButton: true,
  //   confirmButtonText: '确定',
  //   cancelButtonText: '取消',
  //   message: () =>
  //     h(ElSelect, {
  //       modelValue: selectedKnowledgeBase.value,
  //       'onUpdate:modelValue': (val: string) => {
  //         selectedKnowledgeBase.value = val
  //       },
  //     }, () => [...knowledgeBases.value.map((item) => {
  //       return h(ElOption, {
  //         value: item.kb_name,
  //         label: `${item.kb_name}:${item.kb_info}`,
  //       })
  //     })]),
  // }).then(() => {
  //   if (selectedKnowledgeBase.value) {
  //     window.api.createView({ shopId: id, knowledge: selectedKnowledgeBase.value }).then(() => {
  //       getViews()
  //       leftTabsActiveName.value = 'second'
  //       showShopList.value = false
  //       selectedKnowledgeBase.value = ''
  //     })
  //   } else {
  //     ElMessage.error('请选择知识库')
  //   }
  // }).catch(() => { })

}

// 视图管理
const views = ref<any>([])
function getViews() {
  window.api.getViews().then((res: any) => {
    views.value = res
  })
}
function selectView(view) {
  if (view.knowledge == "") {
    window.api.selectView("-1")
    if (knowledgeBases.value.length == 0) {
      getKnowledgeBases()
    }
    ElMessageBox({
      title: '请选择知识库',
      customClass: 'select-view-box',
      showCancelButton: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      message: () =>
        h(ElSelect, {
          modelValue: selectedKnowledgeBase.value,
          'onUpdate:modelValue': (val: string) => {
            selectedKnowledgeBase.value = val
          },
        }, () => [...knowledgeBases.value.map((item) => {
          return h(ElOption, {
            value: item.kb_name,
            label: `${item.kb_name}:${item.kb_info}`,
          })
        })]),
    }).then(() => {
      if (selectedKnowledgeBase.value) {
        window.api.setViewKnowledge(view.viewId, selectedKnowledgeBase.value)
        const viewId = view.viewId
        window.api.selectView(viewId)
        selectedKnowledgeBase.value = ''
      } else {
        ElMessage.error('请选择知识库')
      }
    }).catch(() => { })
  } else {
    const viewId = view.viewId
    window.api.selectView(viewId)
  }
}
function changeDuty(view) {
  window.api.switchDuty(view.viewId).then(() => {
    getViews()
  })
}
function closeView(viewId) {
  window.api.closeView(viewId).then(() => {
    getViews()
  })
}

// 是否显示店铺列表
const showShopList = ref(false)
const shopList = ref<any>([])
function getShopList() {
  window.api.getShop().then((res: any) => {
    shopList.value = res
  })
}

// 定时调用获取视图列表
function getViewListTimer() {
  getViews()
  setTimeout(() => {
    getViewListTimer()
  }, 5000);
}


onMounted(() => {
  getViews()
  getShopList()
  getKnowledgeBases()
  // 检查URL是否包含"login"参数
  // 处理常规查询参数 ?login=true
  const urlParams = new URLSearchParams(window.location.search)
  // 处理hash后的参数 #/path?login=true
  const hashParams = window.location.hash.includes('?')
    ? new URLSearchParams(window.location.hash.split('?')[1])
    : new URLSearchParams('')

  if (urlParams.has('login') || hashParams.has('login')) {
    leftTabsActiveName.value = 'second'
  }
  getViewListTimer()
})

</script>

<style lang="scss" scoped>
// 🎨 协调统一的接待页面设计
.modern-reception {
  height: 100vh;
  background: var(--bg-primary);
  font-family: 'Inter', 'Noto Sans SC', sans-serif;

  .modern-main {
    display: flex;
    height: 100vh;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);

    // 🎯 统一风格的侧边栏
    .modern-sidebar {
      width: 350px;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius-xl);
      box-shadow: var(--shadow-xl);
      border: 1px solid rgba(255, 255, 255, 0.2);
      overflow: hidden;
      display: flex;
      flex-direction: column;

      // 📋 侧边栏头部
      .sidebar-header {
        padding: var(--spacing-lg);
        background: var(--primary-gradient);
        color: white;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: -50%;
          right: -50%;
          width: 200%;
          height: 200%;
          background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
          animation: float 6s ease-in-out infinite;
        }

        .sidebar-title {
          display: flex;
          align-items: center;
          gap: var(--spacing-md);
          margin: 0;
          font-size: 20px;
          font-weight: 700;
          position: relative;
          z-index: 1;

          .title-icon {
            font-size: 24px;
          }
        }
      }

      // 🏷️ 统一风格的标签页系统
      .modern-tabs {
        flex: 1;
        display: flex;
        flex-direction: column;

        :deep(.el-tabs__header) {
          margin: 0;
          padding: 0 var(--spacing-lg) var(--spacing-md);
          background: transparent;
          border: none;

          .el-tabs__nav-wrap {
            &::after {
              display: none;
            }
          }

          .el-tabs__nav {
            border: none;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 12px;
            padding: 3px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.4);

            .el-tabs__item {
              border: none;
              padding: 8px 14px;
              margin: 0 2px;
              border-radius: 9px;
              color: #64748b;
              font-weight: 500;
              font-size: 13px;
              transition: all 0.2s ease;
              position: relative;

              .modern-tab-label {
                display: flex;
                align-items: center;
                gap: 8px;

                .tab-text {
                  font-weight: 500;
                  white-space: nowrap;
                }

                .tab-badge {
                  min-width: 18px;
                  height: 18px;
                  border-radius: 9px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 11px;
                  font-weight: 600;
                  transition: all 0.2s ease;

                  &.online-badge {
                    background: #10b981;
                    color: white;
                  }

                  &.offline-badge {
                    background: #ef4444;
                    color: white;
                  }
                }
              }

              &:hover {
                color: #4f46e5;
                background: rgba(79, 70, 229, 0.08);
              }

              &.is-active {
                color: white;
                background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
                box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);

                .modern-tab-label .tab-badge {
                  background: rgba(255, 255, 255, 0.9);

                  &.online-badge {
                    color: #10b981;
                  }

                  &.offline-badge {
                    color: #ef4444;
                  }
                }
              }
            }
          }
        }

        :deep(.el-tabs__content) {
          flex: 1;
          padding: 0;

          .el-tab-pane {
            height: 100%;
          }
        }
      }

      // 📋 统一风格的视图容器
      .tab-scrollbar {
        flex: 1;

        :deep(.el-scrollbar__view) {
          height: 100%;
        }
      }

      .view-container {
        padding: var(--spacing-lg);
        height: 100%;
      }

      .view-list {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
      }

      .view-item {
        display: flex;
        align-items: center;
        padding: var(--spacing-lg);
        background: rgba(255, 255, 255, 0.8);
        border-radius: var(--border-radius-lg);
        border: 1px solid rgba(255, 255, 255, 0.2);
        cursor: pointer;
        transition: all 0.3s var(--ease-out-cubic);
        box-shadow: var(--shadow-md);
        backdrop-filter: blur(10px);

        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-xl);
          border-color: rgba(102, 126, 234, 0.2);
          background: rgba(255, 255, 255, 0.95);
        }

        &.offline-view {
          opacity: 0.7;
          border-color: rgba(239, 68, 68, 0.2);

          &:hover {
            border-color: rgba(239, 68, 68, 0.3);
          }
        }

        .view-avatar {
          position: relative;
          margin-right: var(--spacing-md);

          .view-image {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius-md);
            object-fit: cover;
            box-shadow: var(--shadow-md);
            border: 2px solid rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
          }

          .online-indicator {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            background: var(--success-color);
            border: 3px solid #fff;
            border-radius: 50%;
            box-shadow: var(--shadow-sm);
            animation: pulse 2s infinite;
          }

          .offline-indicator {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            background: var(--error-color);
            border: 3px solid #fff;
            border-radius: 50%;
            box-shadow: var(--shadow-sm);
          }
        }

        .view-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: var(--spacing-sm);

          .view-header {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .view-name {
              font-size: 16px;
              font-weight: 600;
              color: var(--text-primary);
            }

            .view-status {
              .platform-tag {
                padding: 4px var(--spacing-sm);
                background: var(--primary-gradient);
                color: white;
                border-radius: var(--border-radius-md);
                font-size: 11px;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                box-shadow: var(--shadow-sm);

                &.offline {
                  background: linear-gradient(135deg, var(--error-color), #dc2626);
                }
              }
            }
          }

          .view-controls {
            .duty-switch {
              :deep(.el-switch__core) {
                background: var(--bg-tertiary);
                border-color: var(--border-color);

                &::after {
                  background: #fff;
                  box-shadow: var(--shadow-sm);
                }
              }

              :deep(.is-checked .el-switch__core) {
                background: var(--success-color);
                border-color: var(--success-color);
              }
            }
          }
        }

        .close-btn {
          margin-left: var(--spacing-sm);
          background: rgba(239, 68, 68, 0.1);
          border-color: rgba(239, 68, 68, 0.2);
          color: var(--error-color);

          &:hover {
            background: rgba(239, 68, 68, 0.2);
            border-color: rgba(239, 68, 68, 0.3);
            transform: scale(1.05);
          }
        }
      }

      // 🏪 统一风格的平台选择面板
      .modern-shop-panel {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        z-index: 10;
        border-radius: var(--border-radius-xl);
        display: flex;
        flex-direction: column;
        box-shadow: var(--shadow-xl);

        .shop-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--spacing-lg);
          background: var(--primary-gradient);
          color: white;

          .header-content {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);

            .header-icon {
              font-size: 24px;
            }

            .header-title {
              margin: 0;
              font-size: 20px;
              font-weight: 600;
            }
          }
        }

        .shop-content {
          flex: 1;
          padding: var(--spacing-lg);
          overflow-y: auto;
        }

        .shop-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
          gap: var(--spacing-md);
        }

        .shop-card {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: var(--spacing-lg) var(--spacing-md);
          background: rgba(255, 255, 255, 0.8);
          border-radius: var(--border-radius-lg);
          border: 1px solid rgba(255, 255, 255, 0.2);
          cursor: pointer;
          transition: all 0.3s var(--ease-out-cubic);
          box-shadow: var(--shadow-md);
          backdrop-filter: blur(10px);

          &:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            border-color: rgba(102, 126, 234, 0.2);
          }

          .shop-icon {
            margin-bottom: var(--spacing-md);

            .platform-image {
              width: 40px;
              height: 40px;
              border-radius: var(--border-radius-md);
              box-shadow: var(--shadow-sm);
            }

            .image-placeholder {
              width: 40px;
              height: 40px;
              border-radius: var(--border-radius-md);
              background: var(--bg-secondary);
              display: flex;
              align-items: center;
              justify-content: center;
              color: var(--text-tertiary);

              .el-icon {
                font-size: 20px;
              }
            }
          }

          .shop-info {
            text-align: center;

            .shop-name {
              display: block;
              font-size: 14px;
              font-weight: 600;
              color: var(--text-primary);
              margin-bottom: 4px;
            }

            .shop-desc {
              display: block;
              font-size: 12px;
              color: var(--text-secondary);
            }
          }
        }
      }
    }

    // 🖥️ 统一风格的内容工作台
    .modern-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      border-radius: var(--border-radius-xl);
      box-shadow: var(--shadow-xl);
      border: 1px solid rgba(255, 255, 255, 0.2);
      overflow: hidden;

      .content-header {
        padding: var(--spacing-lg);
        background: var(--primary-gradient);
        color: white;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: -50%;
          right: -50%;
          width: 200%;
          height: 200%;
          background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
          animation: float 6s ease-in-out infinite;
        }

        .header-info {
          position: relative;
          z-index: 1;

          .content-title {
            margin: 0 0 var(--spacing-sm) 0;
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(45deg, #ffffff, #f0f8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }

          .content-desc {
            margin: 0;
            font-size: 14px;
            opacity: 0.9;
          }
        }
      }

      .content-body {
        flex: 1;
        position: relative;

        .embed-container {
          width: 100%;
          height: 100%;
          background: #fff;
        }
      }
    }
  }

  // 📱 统一风格的空状态
  .modern-empty {
    padding: var(--spacing-2xl) var(--spacing-lg);

    :deep(.el-empty__image) {
      margin-bottom: var(--spacing-lg);

      .empty-icon {
        font-size: 64px;
        color: var(--text-tertiary);
        animation: float 6s ease-in-out infinite;
      }
    }

    :deep(.el-empty__description) {
      color: var(--text-secondary);
      font-size: 14px;
      font-weight: 500;
    }
  }
}

// 🎭 统一的动画效果
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

// 🎬 过渡动画
.view-list-enter-active,
.view-list-leave-active {
  transition: all 0.3s var(--ease-out-cubic);
}

.view-list-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.view-list-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.shop-grid-enter-active,
.shop-grid-leave-active {
  transition: all 0.3s var(--ease-out-cubic);
}

.shop-grid-enter-from {
  opacity: 0;
  transform: scale(0.8);
}

.shop-grid-leave-to {
  opacity: 0;
  transform: scale(0.8);
}
</style>
