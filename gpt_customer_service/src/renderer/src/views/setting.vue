<template>
  <div class="modern-settings">
    <!-- 🎯 页面头部 -->
    <div class="settings-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title gradient-text">系统设置</h1>
          <p class="page-subtitle">配置AI智能客服系统参数</p>
        </div>
        <div class="header-actions">
          <button class="modern-btn modern-btn-secondary" @click="resetToDefault">
            <el-icon><Refresh /></el-icon>
            重置默认
          </button>
        </div>
      </div>
    </div>

    <!-- 🎨 设置内容区域 -->
    <div class="settings-content">
      <div class="settings-grid">
        <!-- 🤖 AI模型配置 -->
        <div class="settings-section modern-card">
          <div class="section-header">
            <div class="section-icon">
              <el-icon><Setting /></el-icon>
            </div>
            <div class="section-info">
              <h3>AI模型配置</h3>
              <p>配置智能客服的AI模型参数</p>
            </div>
          </div>

          <div class="section-content">
            <el-form :model="config" label-position="top" v-loading="loading">
              <el-form-item label="AI模型选择" class="modern-form-item">
                <el-select
                  v-model="config.modelName"
                  placeholder="请选择AI模型"
                  class="modern-select"
                  size="large"
                >
                  <el-option
                    v-for="item in modelList"
                    :key="item.id"
                    :label="item.model_code"
                    :value="item.model_code"
                  >
                    <div class="model-option">
                      <span class="model-name">{{ item.model_code }}</span>
                      <span class="model-desc">{{ item.model_name }}</span>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="调试模式" class="modern-form-item">
                <div class="switch-container">
                  <el-switch
                    v-model="config.debug"
                    size="large"
                  />
                  <div class="switch-info">
                    <span class="switch-label">{{ config.debug ? '已启用' : '已禁用' }}</span>
                    <span class="switch-desc">开启后将显示详细的调试信息</span>
                  </div>
                </div>
              </el-form-item>

              <el-form-item class="form-actions">
                <button
                  class="modern-btn modern-btn-primary"
                  @click="saveConfig"
                  :disabled="loading"
                >
                  <el-icon v-if="loading"><Loading /></el-icon>
                  <el-icon v-else><Select /></el-icon>
                  {{ loading ? '保存中...' : '保存配置' }}
                </button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 📊 系统监控 -->
        <div class="settings-section modern-card">
          <div class="section-header">
            <div class="section-icon">
              <el-icon><View /></el-icon>
            </div>
            <div class="section-info">
              <h3>系统监控</h3>
              <p>查看系统运行状态和日志信息</p>
            </div>
          </div>

          <div class="section-content">
            <div class="monitor-stats">
              <div class="stat-card">
                <div class="stat-icon success">
                  <el-icon><SuccessFilled /></el-icon>
                </div>
                <div class="stat-info">
                  <span class="stat-value">正常</span>
                  <span class="stat-label">系统状态</span>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon primary">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="stat-info">
                  <span class="stat-value">{{ uptime }}</span>
                  <span class="stat-label">运行时间</span>
                </div>
              </div>

              <div class="stat-card">
                <div class="stat-icon warning">
                  <el-icon><Files /></el-icon>
                </div>
                <div class="stat-info">
                  <span class="stat-value">{{ logLines }}</span>
                  <span class="stat-label">日志条数</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 📝 日志查看器 -->
      <div class="log-viewer modern-card">
        <div class="log-header">
          <div class="log-title">
            <el-icon><Files /></el-icon>
            <span>系统日志</span>
          </div>
          <div class="log-actions">
            <button class="modern-btn modern-btn-secondary" @click="getLog">
              <el-icon><Refresh /></el-icon>
              刷新日志
            </button>
            <button class="modern-btn modern-btn-secondary" @click="clearLog">
              <el-icon><DeleteFilled /></el-icon>
              清空日志
            </button>
          </div>
        </div>

        <div class="log-content">
          <div class="log-terminal" ref="logTerminal">
            <pre class="log-text">{{ logText || '暂无日志信息...' }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, nextTick, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus'

interface AppConfig {
  apiKey: string;
  modelName: string;
  debug: boolean;
}

const config = ref<AppConfig>({
  apiKey: '',
  modelName: '',
  debug: false
})
const loading = ref(false)

// 系统监控数据
const uptime = ref('2小时30分钟')
const logLines = computed(() => {
  return logText.value ? logText.value.split('\n').length : 0
})

function getConfig() {
  window.api.getConfig().then((res: any) => {
    config.value = res
  })
}

function saveConfig() {
  loading.value = true
  window.api.setConfig({
    apiKey: config.value.apiKey,
    modelName: config.value.modelName,
    debug: config.value.debug
  }).then(() => {
    loading.value = false
    ElMessage({
      message: '配置保存成功！',
      type: 'success',
    })
  }).catch(() => {
    loading.value = false
    ElMessage({
      message: '配置保存失败！',
      type: 'error',
    })
  })
}

function resetToDefault() {
  ElMessageBox.confirm(
    '确定要重置为默认配置吗？此操作不可撤销。',
    '重置确认',
    {
      confirmButtonText: '确定重置',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    config.value = {
      apiKey: '',
      modelName: '',
      debug: false
    }
    ElMessage({
      message: '已重置为默认配置',
      type: 'success',
    })
  }).catch(() => {
    // 用户取消
  })
}

interface Model {
  id: Number;
  model_name: string;
  model_code: string;
}

const modelList = ref<Array<Model>>([])

function getModelList() {
  window.api.getModelListApi({
    "platform_type": "",
    "model_type": "llm_models"
  }).then(res => {
    if (res.code !== 200) {
      let msg = '获取模型列表失败，请检查ApiKey'
      if (res.msg) {
        msg = res.msg
      } else if (res.message) {
        msg = res.message
      } else if (res.data) {
        msg = res.data
      } else {
        msg = JSON.stringify(res)
      }
      ElMessageBox.alert(msg, '提示', {
        confirmButtonText: '确认'
      })
    } else {
      modelList.value = res.data
    }
  }).catch(err => {
    let msg = ""
    if (err.msg) {
      msg = err.msg
    } else if (err.message) {
      msg = err.message
    } else {
      msg = JSON.stringify(err)
    }

    ElMessageBox.alert(msg, '提示', {
      confirmButtonText: '确认',
      callback: () => {
      },
    })
  })
}

// 日志管理
const logText = ref('')
const logTerminal = ref<HTMLElement>()

function getLog() {
  window.api.getLog().then(res => {
    logText.value = res
    nextTick(() => {
      if (logTerminal.value) {
        logTerminal.value.scrollTop = logTerminal.value.scrollHeight
      }
    })
  })
}

function clearLog() {
  ElMessageBox.confirm(
    '确定要清空所有日志吗？此操作不可撤销。',
    '清空确认',
    {
      confirmButtonText: '确定清空',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    logText.value = ''
    ElMessage({
      message: '日志已清空',
      type: 'success',
    })
  }).catch(() => {
    // 用户取消
  })
}

// 定时更新运行时间
let uptimeInterval: NodeJS.Timeout
const startTime = Date.now()

function updateUptime() {
  const now = Date.now()
  const diff = now - startTime
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  uptime.value = `${hours}小时${minutes}分钟`
}

onMounted(() => {
  getConfig()
  getModelList()
  getLog()

  // 每分钟更新一次运行时间
  updateUptime()
  uptimeInterval = setInterval(updateUptime, 60000)
})

onUnmounted(() => {
  if (uptimeInterval) {
    clearInterval(uptimeInterval)
  }
})
</script>

<style lang="scss" scoped>
/* 🎨 现代化设置页面样式 */
.modern-settings {
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  overflow-y: auto;
}

/* 🎯 页面头部 */
.settings-header {
  padding: var(--spacing-xl) var(--spacing-lg);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
  }

  .header-info {
    .page-title {
      font-size: 32px;
      font-weight: 700;
      margin: 0 0 4px 0;
    }

    .page-subtitle {
      font-size: 16px;
      color: var(--text-secondary);
      margin: 0;
    }
  }

  .header-actions {
    display: flex;
    gap: var(--spacing-md);
  }
}

/* 🎨 设置内容区域 */
.settings-content {
  padding: var(--spacing-xl) var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

/* 🔧 设置区块 */
.settings-section {
  padding: var(--spacing-xl);

  .section-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);

    .section-icon {
      width: 48px;
      height: 48px;
      background: var(--primary-gradient);
      border-radius: var(--border-radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      color: white;
      box-shadow: var(--shadow-md);
    }

    .section-info {
      h3 {
        font-size: 20px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 4px 0;
      }

      p {
        font-size: 14px;
        color: var(--text-secondary);
        margin: 0;
      }
    }
  }

  .section-content {
    .modern-form-item {
      margin-bottom: var(--spacing-lg);

      :deep(.el-form-item__label) {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
      }

      .modern-select {
        width: 100%;

        .model-option {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .model-name {
            font-weight: 600;
            color: var(--text-primary);
          }

          .model-desc {
            font-size: 12px;
            color: var(--text-secondary);
          }
        }
      }

      .switch-container {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);

        .switch-info {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .switch-label {
            font-weight: 600;
            color: var(--text-primary);
          }

          .switch-desc {
            font-size: 12px;
            color: var(--text-secondary);
          }
        }
      }
    }

    .form-actions {
      margin-top: var(--spacing-xl);
      text-align: center;
    }
  }
}

/* 📊 监控统计卡片 */
.monitor-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);

  .stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-glass);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);

    .stat-icon {
      width: 40px;
      height: 40px;
      border-radius: var(--border-radius-sm);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: white;

      &.success { background: var(--success-color); }
      &.primary { background: var(--primary-color); }
      &.warning { background: var(--warning-color); }
    }

    .stat-info {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .stat-value {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
      }

      .stat-label {
        font-size: 12px;
        color: var(--text-secondary);
      }
    }
  }
}

/* 📝 日志查看器 */
.log-viewer {
  padding: 0;
  overflow: hidden;

  .log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));

    .log-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .log-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
  }

  .log-content {
    height: 400px;
    overflow: hidden;

    .log-terminal {
      height: 100%;
      background: #1a1a1a;
      color: #00ff00;
      font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      line-height: 1.4;
      overflow-y: auto;
      padding: var(--spacing-md);
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
          radial-gradient(circle at 20% 50%, rgba(0, 255, 0, 0.03) 0%, transparent 50%),
          radial-gradient(circle at 80% 50%, rgba(0, 255, 0, 0.03) 0%, transparent 50%);
        pointer-events: none;
      }

      .log-text {
        margin: 0;
        white-space: pre-wrap;
        word-break: break-word;
        position: relative;
        z-index: 1;
        text-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
      }

      /* 滚动条样式 */
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #2a2a2a;
      }

      &::-webkit-scrollbar-thumb {
        background: #00ff00;
        border-radius: 4px;
        box-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #00cc00;
      }
    }
  }
}

/* 🎭 响应式设计 */
@media (max-width: 768px) {
  .settings-header .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .settings-grid {
    grid-template-columns: 1fr;
  }

  .monitor-stats {
    grid-template-columns: 1fr;
  }

  .log-header {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .log-actions {
    justify-content: center;
  }
}

/* 🎨 Element Plus 组件样式覆盖 */
:deep(.el-select) {
  .el-input__wrapper {
    background: var(--bg-glass);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-md);
    transition: all 0.3s var(--ease-out-cubic);

    &:hover {
      border-color: var(--primary-color);
    }

    &.is-focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
}

:deep(.el-switch) {
  &.is-checked .el-switch__core {
    background-color: var(--primary-color);
  }
}

:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
}
</style>
