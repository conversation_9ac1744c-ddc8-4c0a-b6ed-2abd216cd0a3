<template>
  <div class="modern-login">
    <!-- 🌟 背景装饰 -->
    <div class="bg-decoration">
      <div class="bg-gradient"></div>
      <div class="bg-pattern"></div>
      <div class="floating-elements">
        <div class="float-element float-1"></div>
        <div class="float-element float-2"></div>
        <div class="float-element float-3"></div>
      </div>
    </div>

    <!-- 🎯 现代化顶部栏 -->
    <div class="modern-header">
      <div class="header-brand">
        <div class="brand-icon">
          <div class="ai-logo">
            <div class="logo-circle"></div>
            <div class="logo-pulse"></div>
          </div>
        </div>
        <div class="brand-text">
          <span class="brand-title gradient-text">AI智能客服</span>
        </div>
      </div>

      <div class="window-controls">
        <button class="control-btn minimize-btn" @click="minimizeWindow">
          <el-icon><Minus /></el-icon>
        </button>
        <button class="control-btn maximize-btn" @click="toggleMaximize">
          <el-icon>
            <svg v-if="!isMaximized" t="1747999627920" class="icon" viewBox="0 0 1024 1024" version="1.1"
              xmlns="http://www.w3.org/2000/svg" p-id="1730" width="256" height="256">
              <path
                d="M812.3 959.4H213.7c-81.6 0-148-66.4-148-148V212.9c0-81.6 66.4-148 148-148h598.5c81.6 0 148 66.4 148 148v598.5C960.3 893 893.9 959.4 812.3 959.4zM213.7 120.9c-50.7 0-92 41.3-92 92v598.5c0 50.7 41.3 92 92 92h598.5c50.7 0 92-41.3 92-92V212.9c0-50.7-41.3-92-92-92H213.7z"
                fill="" p-id="1731"></path>
            </svg>
            <svg v-else t="1747999659229" class="icon" viewBox="0 0 1024 1024" version="1.1"
              xmlns="http://www.w3.org/2000/svg" p-id="1878" width="256" height="256">
              <path
                d="M812.2 65H351.6c-78.3 0-142.5 61.1-147.7 138.1-77 5.1-138.1 69.4-138.1 147.7v460.6c0 81.6 66.4 148 148 148h460.6c78.3 0 142.5-61.1 147.7-138.1 77-5.1 138.1-69.4 138.1-147.7V213c0-81.6-66.4-148-148-148z m-45.8 746.3c0 50.7-41.3 92-92 92H213.8c-50.7 0-92-41.3-92-92V350.7c0-50.7 41.3-92 92-92h460.6c50.7 0 92 41.3 92 92v460.6z m137.8-137.7c0 47.3-35.8 86.3-81.8 91.4V350.7c0-81.6-66.4-148-148-148H260.2c5.1-45.9 44.2-81.8 91.4-81.8h460.6c50.7 0 92 41.3 92 92v460.7z"
                fill="" p-id="1879"></path>
            </svg>
          </el-icon>
        </button>
        <button class="control-btn close-btn" @click="closeWindow">
          <el-icon><Close /></el-icon>
        </button>
      </div>
    </div>

    <!-- 🎨 主登录区域 -->
    <div class="login-main">
      <div class="login-container">
        <!-- 左侧装饰区域 -->
        <div class="login-decoration">
          <div class="decoration-content">
            <div class="welcome-text">
              <h2 class="welcome-title gradient-text">欢迎使用</h2>
              <h1 class="system-title">GPT智能客服系统</h1>
              <p class="system-desc">Professional AI Customer Service Platform</p>
            </div>
            <div class="feature-list">
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><ChatDotRound /></el-icon>
                </div>
                <span>智能对话</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><Monitor /></el-icon>
                </div>
                <span>多平台接入</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">
                  <el-icon><DataAnalysis /></el-icon>
                </div>
                <span>数据分析</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧登录/注册表单 -->
        <div class="login-form-section">
          <div class="login-card">
            <!-- 标签页切换 -->
            <div class="form-tabs">
              <button
                :class="['tab-btn', { active: currentTab === 'login' }]"
                @click="switchTab('login')"
              >
                账户登录
              </button>
              <button
                :class="['tab-btn', { active: currentTab === 'register' }]"
                @click="switchTab('register')"
              >
                用户注册
              </button>
              <div class="tab-indicator" :style="{ transform: `translateX(${currentTab === 'login' ? '0' : '100%'})` }"></div>
            </div>

            <div class="card-header">
              <h3 class="login-title">{{ currentTab === 'login' ? '账户登录' : '用户注册' }}</h3>
              <p class="login-subtitle">{{ currentTab === 'login' ? '请输入您的登录凭据' : '创建您的新账户' }}</p>
            </div>

            <!-- 登录表单 -->
            <el-form
              v-show="currentTab === 'login'"
              class="modern-login-form"
              :model="loginForm"
              :rules="loginRules"
              ref="loginFormRef"
            >
              <el-form-item prop="username" class="form-item">
                <div class="input-wrapper">
                  <el-input
                    v-model="loginForm.username"
                    placeholder="手机号/用户名"
                    prefix-icon="User"
                    clearable
                    class="modern-input"
                    size="large"
                  />
                </div>
              </el-form-item>

              <el-form-item prop="password" class="form-item">
                <div class="input-wrapper">
                  <el-input
                    v-model="loginForm.password"
                    placeholder="请输入密码"
                    type="password"
                    prefix-icon="Lock"
                    show-password
                    clearable
                    class="modern-input"
                    size="large"
                    @keyup.enter="handleLogin"
                  />
                </div>
              </el-form-item>

              <el-form-item class="form-item">
                <button
                  type="button"
                  class="modern-login-btn"
                  @click="handleLogin"
                  :disabled="isLoading"
                >
                  <span v-if="!isLoading" class="btn-text">立即登录</span>
                  <span v-else class="btn-text">
                    <el-icon class="loading-icon"><Loading /></el-icon>
                    登录中...
                  </span>
                  <div class="btn-shine"></div>
                </button>
              </el-form-item>
            </el-form>

            <!-- 注册表单 -->
            <el-form
              v-show="currentTab === 'register'"
              class="modern-login-form"
              :model="registerForm"
              :rules="registerRules"
              ref="registerFormRef"
            >
              <el-form-item prop="username" class="form-item">
                <div class="input-wrapper">
                  <el-input
                    v-model="registerForm.username"
                    placeholder="请输入用户名"
                    prefix-icon="User"
                    clearable
                    class="modern-input"
                    size="large"
                  />
                </div>
              </el-form-item>

              <el-form-item prop="email" class="form-item">
                <div class="input-wrapper">
                  <el-input
                    v-model="registerForm.email"
                    placeholder="请输入邮箱地址"
                    prefix-icon="Message"
                    clearable
                    class="modern-input"
                    size="large"
                  />
                </div>
              </el-form-item>

              <el-form-item prop="phone" class="form-item">
                <div class="input-wrapper">
                  <el-input
                    v-model="registerForm.phone"
                    placeholder="请输入手机号码"
                    prefix-icon="Phone"
                    clearable
                    class="modern-input"
                    size="large"
                  />
                </div>
              </el-form-item>

              <el-form-item prop="password" class="form-item">
                <div class="input-wrapper">
                  <el-input
                    v-model="registerForm.password"
                    placeholder="请输入密码"
                    type="password"
                    prefix-icon="Lock"
                    show-password
                    clearable
                    class="modern-input"
                    size="large"
                  />
                </div>
              </el-form-item>

              <el-form-item prop="confirmPassword" class="form-item">
                <div class="input-wrapper">
                  <el-input
                    v-model="registerForm.confirmPassword"
                    placeholder="请确认密码"
                    type="password"
                    prefix-icon="Lock"
                    show-password
                    clearable
                    class="modern-input"
                    size="large"
                    @keyup.enter="handleRegister"
                  />
                </div>
              </el-form-item>

              <el-form-item class="form-item">
                <button
                  type="button"
                  class="modern-login-btn"
                  @click="handleRegister"
                  :disabled="isLoading"
                >
                  <span v-if="!isLoading" class="btn-text">立即注册</span>
                  <span v-else class="btn-text">
                    <el-icon class="loading-icon"><Loading /></el-icon>
                    注册中...
                  </span>
                  <div class="btn-shine"></div>
                </button>
              </el-form-item>
            </el-form>

            <div class="login-footer">
              <div class="footer-links">
                <a href="#" class="footer-link">忘记密码？</a>
                <a href="#" class="footer-link">联系管理员</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

// 窗口状态
const isMaximized = ref(false)
const isLoading = ref(false)

// 当前标签页 - 默认显示登录
const currentTab = ref('login')

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 注册表单数据
const registerForm = reactive({
  username: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: ''
})

// 表单引用
const loginFormRef = ref()
const registerFormRef = ref()

// 登录表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 注册表单验证规则
const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
    { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 标签页切换
const switchTab = (tab) => {
  currentTab.value = tab
  // 清空表单数据
  if (tab === 'login') {
    Object.assign(loginForm, { username: '', password: '' })
    loginFormRef.value?.clearValidate()
  } else {
    Object.assign(registerForm, {
      username: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: ''
    })
    registerFormRef.value?.clearValidate()
  }
}

// 窗口控制方法
const minimizeWindow = () => {
  try {
    // 调用Electron API最小化窗口
    window.api.minimize()
  } catch (error) {
    console.error('最小化窗口失败:', error)
  }
}

const toggleMaximize = () => {
  try {
    // 调用Electron API最大化/还原窗口
    window.api.maximize(isMaximized.value)
    isMaximized.value = !isMaximized.value
  } catch (error) {
    console.error('切换窗口状态失败:', error)
  }
}

const closeWindow = () => {
  try {
    // 调用Electron API关闭窗口
    window.api.quit()
  } catch (error) {
    console.error('关闭窗口失败:', error)
  }
}

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    isLoading.value = true

    // 模拟登录请求
    setTimeout(() => {
      isLoading.value = false
      ElMessage.success('登录成功！')
      // 登录成功后跳转到主界面
      window.location.hash = '#/main/index/home'
    }, 2000)

  } catch (error) {
    console.error('登录验证失败:', error)
    isLoading.value = false
  }
}

// 注册处理
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    const valid = await registerFormRef.value.validate()
    if (!valid) return

    isLoading.value = true

    // 模拟注册请求
    setTimeout(() => {
      isLoading.value = false
      ElMessage.success('注册成功！请登录')
      // 注册成功后切换到登录页面
      switchTab('login')
    }, 2000)

  } catch (error) {
    console.error('注册验证失败:', error)
    isLoading.value = false
  }
}

// 窗口大小监听
const windowSize = ref({
  width: window.innerWidth,
  height: window.innerHeight
})

// 更新窗口大小
const updateWindowSize = () => {
  windowSize.value = {
    width: window.innerWidth,
    height: window.innerHeight
  }
}

// 组件挂载时的初始化
onMounted(() => {
  // 添加窗口大小监听
  window.addEventListener('resize', updateWindowSize)
  updateWindowSize()
  console.log('登录页面已加载')
})

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', updateWindowSize)
})
</script>

<style lang="scss" scoped>
/* 🎨 现代化登录界面样式 - 自适应窗口 */
.modern-login {
  width: 100vw;
  height: 100vh;
  min-width: 320px;
  min-height: 480px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  font-family: 'Inter', 'Noto Sans SC', sans-serif;

  /* 🌟 背景装饰 */
  .bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    pointer-events: none;

    .bg-gradient {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.9) 0%,
        rgba(118, 75, 162, 0.9) 25%,
        rgba(79, 172, 254, 0.8) 50%,
        rgba(240, 147, 251, 0.8) 75%,
        rgba(102, 126, 234, 0.9) 100%
      );
      animation: gradientShift 15s ease-in-out infinite;
    }

    .bg-pattern {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
      background-size: 400px 400px, 600px 600px;
      animation: patternMove 20s linear infinite;
    }

    .floating-elements {
      position: absolute;
      width: 100%;
      height: 100%;

      .float-element {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        animation: float 6s ease-in-out infinite;

        &.float-1 {
          width: 80px;
          height: 80px;
          top: 20%;
          left: 10%;
          animation-delay: 0s;
        }

        &.float-2 {
          width: 120px;
          height: 120px;
          top: 60%;
          right: 15%;
          animation-delay: 2s;
        }

        &.float-3 {
          width: 60px;
          height: 60px;
          bottom: 20%;
          left: 20%;
          animation-delay: 4s;
        }
      }
    }
  }

  /* 🎯 现代化顶部栏 */
  .modern-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    padding: 0 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    -webkit-app-region: drag;
    z-index: 10;

    * {
      -webkit-app-region: no-drag;
    }

    .header-brand {
      display: flex;
      align-items: center;
      gap: 12px;

      .brand-icon {
        position: relative;
        width: 40px;
        height: 40px;

        .ai-logo {
          position: relative;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .logo-circle {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
            position: relative;
            z-index: 2;

            &::before {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 16px;
              height: 16px;
              background: white;
              border-radius: 50%;
            }
          }

          .logo-pulse {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border: 2px solid rgba(79, 172, 254, 0.6);
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
          }
        }
      }

      .brand-text {
        .brand-title {
          font-size: 18px;
          font-weight: 600;
          margin: 0;
        }
      }
    }

    .window-controls {
      display: flex;
      gap: 8px;

      .control-btn {
        width: 40px;
        height: 32px;
        border: none;
        border-radius: var(--border-radius-sm);
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        color: white;
        cursor: pointer;
        transition: all 0.3s var(--ease-out-cubic);
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }

        &.close-btn:hover {
          background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }

        .el-icon {
          font-size: 14px;
        }
      }
    }
  }

  /* 🎨 主登录区域 - 自适应 */
  .login-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: clamp(16px, 4vw, 40px) clamp(12px, 3vw, 20px);
    position: relative;
    z-index: 1;
    min-height: 0; /* 允许flex收缩 */

    .login-container {
      display: flex;
      width: 100%;
      max-width: min(1200px, 95vw);
      min-height: clamp(480px, 70vh, 680px);
      max-height: 90vh;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(25px);
      border-radius: clamp(12px, 2vw, 25px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: var(--shadow-2xl);
      overflow: hidden;
      animation: slideInUp 0.8s var(--ease-out-cubic);

      /* 左侧装饰区域 */
      .login-decoration {
        flex: 1;
        background: linear-gradient(135deg,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0.05) 100%
        );
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 60px 40px;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
          opacity: 0.3;
        }

        .decoration-content {
          position: relative;
          z-index: 2;
          text-align: center;
          color: white;

          .welcome-text {
            margin-bottom: 40px;

            .welcome-title {
              font-size: 24px;
              font-weight: 300;
              margin-bottom: 8px;
              opacity: 0.9;
            }

            .system-title {
              font-size: 36px;
              font-weight: 700;
              margin-bottom: 16px;
              line-height: 1.2;
              text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            }

            .system-desc {
              font-size: 16px;
              opacity: 0.8;
              font-weight: 300;
              letter-spacing: 1px;
            }
          }

          .feature-list {
            display: flex;
            flex-direction: column;
            gap: 20px;

            .feature-item {
              display: flex;
              align-items: center;
              gap: 16px;
              padding: 16px 20px;
              background: rgba(255, 255, 255, 0.1);
              border-radius: var(--border-radius-md);
              backdrop-filter: blur(10px);
              border: 1px solid rgba(255, 255, 255, 0.1);
              transition: all 0.3s var(--ease-out-cubic);

              &:hover {
                background: rgba(255, 255, 255, 0.15);
                transform: translateX(8px);
              }

              .feature-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: linear-gradient(135deg, #4facfe, #00f2fe);
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 18px;
              }

              span {
                font-size: 16px;
                font-weight: 500;
              }
            }
          }
        }
      }

      /* 右侧登录表单 - 自适应 */
      .login-form-section {
        flex: 0 0 clamp(400px, 40vw, 520px);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: clamp(20px, 4vw, 40px);
        position: relative;
        overflow-y: auto; /* 允许滚动 */

        &::before {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 1px;
          height: 100%;
          background: linear-gradient(to bottom,
            transparent,
            rgba(255, 255, 255, 0.3) 50%,
            transparent
          );
        }

        .login-card {
          width: 100%;
          max-width: 440px;

          /* 🎯 标签页切换 */
          .form-tabs {
            display: flex;
            position: relative;
            background: rgba(248, 250, 252, 0.8);
            border-radius: var(--border-radius-md);
            padding: 4px;
            margin-bottom: 24px; /* 减少底部间距 */
            border: 1px solid rgba(226, 232, 240, 0.8);

            .tab-btn {
              flex: 1;
              padding: 12px 20px;
              border: none;
              background: transparent;
              color: var(--text-secondary);
              font-size: 15px;
              font-weight: 500;
              cursor: pointer;
              border-radius: var(--border-radius-sm);
              transition: all 0.3s var(--ease-out-cubic);
              position: relative;
              z-index: 2;

              &.active {
                color: var(--primary-color);
                font-weight: 600;
              }

              &:hover:not(.active) {
                color: var(--text-primary);
              }
            }

            .tab-indicator {
              position: absolute;
              top: 4px;
              left: 4px;
              width: calc(50% - 4px);
              height: calc(100% - 8px);
              background: white;
              border-radius: var(--border-radius-sm);
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              transition: transform 0.3s var(--ease-out-cubic);
              z-index: 1;
            }
          }

          .card-header {
            text-align: center;
            margin-bottom: 24px; /* 减少底部间距 */

            .login-title {
              font-size: 28px;
              font-weight: 700;
              color: var(--text-primary);
              margin-bottom: 6px; /* 减少标题和副标题间距 */
            }

            .login-subtitle {
              font-size: 14px;
              color: var(--text-secondary);
              font-weight: 400;
            }
          }

          .modern-login-form {
            .form-item {
              margin-bottom: 20px; /* 减少间距，让布局更紧凑 */

              &:last-child {
                margin-bottom: 0;
              }

              .input-wrapper {
                position: relative;
                width: 100%; /* 确保输入框占满容器宽度 */

                :deep(.el-input) {
                  width: 100%; /* 确保输入框宽度一致 */

                  .el-input__wrapper {
                    background: rgba(248, 250, 252, 0.8);
                    border: 1px solid rgba(226, 232, 240, 0.8);
                    border-radius: var(--border-radius-md);
                    box-shadow: var(--shadow-sm);
                    transition: all 0.3s var(--ease-out-cubic);
                    backdrop-filter: blur(10px);
                    width: 100%; /* 确保wrapper占满宽度 */

                    &:hover {
                      border-color: var(--primary-color);
                      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                    }

                    &.is-focus {
                      border-color: var(--primary-color);
                      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.15);
                    }

                    .el-input__inner {
                      height: 48px; /* 统一输入框高度 */
                      font-size: 15px;
                      color: var(--text-primary);
                      padding: 0 16px;
                      width: 100%; /* 确保内部输入框占满宽度 */

                      &::placeholder {
                        color: var(--text-tertiary);
                      }
                    }

                    .el-input__prefix {
                      color: var(--text-secondary);
                    }
                  }
                }
              }
            }

            /* 🔘 现代化登录按钮 */
            .modern-login-btn {
              width: 100%;
              height: 48px; /* 与输入框高度保持一致 */
              border: none;
              border-radius: var(--border-radius-md);
              background: var(--primary-gradient);
              color: white;
              font-size: 16px;
              font-weight: 600;
              cursor: pointer;
              position: relative;
              overflow: hidden;
              transition: all 0.3s var(--ease-out-cubic);
              box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
              margin-top: 8px; /* 添加顶部间距 */

              &:hover:not(:disabled) {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
              }

              &:active:not(:disabled) {
                transform: translateY(0);
              }

              &:disabled {
                opacity: 0.7;
                cursor: not-allowed;
              }

              .btn-text {
                position: relative;
                z-index: 2;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;

                .loading-icon {
                  animation: spin 1s linear infinite;
                }
              }

              .btn-shine {
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg,
                  transparent,
                  rgba(255, 255, 255, 0.3),
                  transparent
                );
                transition: left 0.6s;
              }

              &:hover .btn-shine {
                left: 100%;
              }
            }

            .login-footer {
              margin-top: 24px; /* 减少顶部间距 */
              text-align: center;

              .footer-links {
                display: flex;
                justify-content: center;
                gap: 24px;

                .footer-link {
                  color: var(--text-secondary);
                  text-decoration: none;
                  font-size: 14px;
                  transition: color 0.3s var(--ease-out-cubic);

                  &:hover {
                    color: var(--primary-color);
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  /* 🎭 渐变文字效果 */
  .gradient-text {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* 🎬 动画定义 */
@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes patternMove {
  0% {
    background-position: 0 0, 0 0;
  }
  100% {
    background-position: 400px 400px, 600px 600px;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
  }
  66% {
    transform: translateY(10px) rotate(240deg);
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 📱 响应式设计 - 自动适配窗口大小 */

/* 超大屏幕 (>1400px) */
@media (min-width: 1400px) {
  .modern-login {
    .login-main {
      .login-container {
        max-width: 1400px;
        min-height: 750px;

        .login-decoration {
          padding: 80px 60px;

          .decoration-content {
            .welcome-text {
              .system-title {
                font-size: 42px;
              }
            }

            .feature-list {
              gap: 24px;

              .feature-item {
                padding: 20px 24px;
              }
            }
          }
        }

        .login-form-section {
          flex: 0 0 580px;
          padding: 60px 50px;

          .login-card {
            max-width: 480px;
          }
        }
      }
    }
  }
}

/* 大屏幕 (1200px-1400px) */
@media (min-width: 1200px) and (max-width: 1399px) {
  .modern-login {
    .login-main {
      .login-container {
        max-width: 1200px;
        min-height: 680px;
      }
    }
  }
}

/* 中等屏幕 (1024px-1199px) */
@media (min-width: 1024px) and (max-width: 1199px) {
  .modern-login {
    .login-main {
      padding: 30px 20px;

      .login-container {
        max-width: 1000px;
        min-height: 620px;

        .login-decoration {
          padding: 50px 30px;

          .decoration-content {
            .welcome-text {
              .system-title {
                font-size: 32px;
              }
            }
          }
        }

        .login-form-section {
          flex: 0 0 460px;
          padding: 50px 30px;

          .login-card {
            max-width: 400px;

            .modern-login-form {
              .form-item {
                margin-bottom: 24px;

                .input-wrapper {
                  :deep(.el-input) {
                    .el-input__wrapper {
                      .el-input__inner {
                        height: 48px;
                      }
                    }
                  }
                }
              }

              .modern-login-btn {
                height: 52px;
              }
            }
          }
        }
      }
    }
  }
}

/* 平板屏幕 (768px-1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .modern-login {
    .login-main {
      padding: 15px;

      .login-container {
        flex-direction: column;
        min-height: auto;
        max-width: 100%;

        .login-decoration {
          flex: none;
          padding: 20px 30px 15px; /* 大幅减少内边距 */

          .decoration-content {
            .welcome-text {
              margin-bottom: 15px; /* 减少间距 */

              .welcome-title {
                font-size: 18px; /* 减小字体 */
              }

              .system-title {
                font-size: 24px; /* 减小字体 */
              }

              .system-desc {
                font-size: 13px;
              }
            }

            .feature-list {
              display: none; /* 隐藏特性列表节省空间 */
            }
          }
        }

        .login-form-section {
          flex: none;
          padding: 15px 30px 25px; /* 减少内边距 */

          .login-card {
            max-width: 100%;
            width: 100%;

            .form-tabs {
              margin-bottom: 15px; /* 减少间距 */
            }

            .card-header {
              margin-bottom: 15px; /* 减少间距 */

              .login-title {
                font-size: 22px; /* 减小字体 */
              }

              .login-subtitle {
                font-size: 13px;
              }
            }

            .modern-login-form {
              .form-item {
                margin-bottom: 14px; /* 减少间距 */

                .input-wrapper {
                  :deep(.el-input) {
                    .el-input__wrapper {
                      .el-input__inner {
                        height: 44px; /* 减少高度 */
                      }
                    }
                  }
                }
              }

              .modern-login-btn {
                height: 48px; /* 减少高度 */
              }
            }
          }
        }
      }
    }
  }
}

/* 小屏幕 (480px-767px) */
@media (min-width: 480px) and (max-width: 767px) {
  .modern-login {
    .modern-header {
      height: 45px; /* 减少头部高度 */
      padding: 0 16px;

      .header-brand {
        .brand-text .brand-title {
          font-size: 15px; /* 减小字体 */
        }
      }

      .window-controls {
        gap: 6px;

        .control-btn {
          width: 34px; /* 减小按钮 */
          height: 26px;
        }
      }
    }

    .login-main {
      padding: 8px; /* 减少外边距 */

      .login-container {
        flex-direction: column;
        min-height: auto;
        border-radius: var(--border-radius-lg);

        .login-decoration {
          padding: 12px 20px 8px; /* 大幅减少内边距 */

          .decoration-content {
            .welcome-text {
              margin-bottom: 8px; /* 大幅减少间距 */

              .welcome-title {
                font-size: 16px; /* 减小字体 */
              }

              .system-title {
                font-size: 18px; /* 减小字体 */
              }

              .system-desc {
                font-size: 12px;
                margin-top: 4px; /* 减少间距 */
              }
            }

            .feature-list {
              display: none; /* 隐藏特性列表 */
            }
          }
        }

        .login-form-section {
          padding: 12px 20px 20px; /* 大幅减少内边距 */

          .login-card {
            .form-tabs {
              margin-bottom: 12px; /* 减少间距 */

              .tab-btn {
                padding: 6px 12px; /* 减少内边距 */
                font-size: 13px;
              }
            }

            .card-header {
              margin-bottom: 12px; /* 减少间距 */

              .login-title {
                font-size: 20px; /* 减小字体 */
              }

              .login-subtitle {
                font-size: 12px;
              }
            }

            .modern-login-form {
              .form-item {
                margin-bottom: 12px; /* 大幅减少间距 */

                .input-wrapper {
                  :deep(.el-input) {
                    .el-input__wrapper {
                      .el-input__inner {
                        height: 38px; /* 大幅减少高度 */
                        font-size: 13px;
                      }
                    }
                  }
                }
              }

              .modern-login-btn {
                height: 42px; /* 大幅减少高度 */
                font-size: 14px;
              }
            }

            .login-footer {
              margin-top: 15px; /* 减少间距 */

              .footer-links {
                gap: 12px;

                .footer-link {
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }
  }
}

/* 超小屏幕 (<480px) */
@media (max-width: 479px) {
  .modern-login {
    .modern-header {
      height: 40px; /* 进一步减少头部高度 */
      padding: 0 10px;

      .header-brand {
        .brand-icon {
          .ai-logo {
            .logo-circle {
              width: 24px; /* 减小logo */
              height: 24px;

              &::before {
                width: 10px;
                height: 10px;
              }
            }

            .logo-pulse {
              width: 28px;
              height: 28px;
            }
          }
        }

        .brand-text .brand-title {
          font-size: 13px; /* 减小字体 */
        }
      }

      .window-controls {
        gap: 3px;

        .control-btn {
          width: 28px; /* 减小按钮 */
          height: 22px;

          .el-icon {
            font-size: 11px;
          }
        }
      }
    }

    .login-main {
      padding: 6px; /* 进一步减少外边距 */

      .login-container {
        border-radius: var(--border-radius-md);
        min-height: auto;

        .login-decoration {
          padding: 8px 16px 6px; /* 极大减少内边距 */

          .decoration-content {
            .welcome-text {
              margin-bottom: 6px; /* 极大减少间距 */

              .welcome-title {
                font-size: 14px; /* 减小字体 */
              }

              .system-title {
                font-size: 16px; /* 减小字体 */
              }

              .system-desc {
                font-size: 11px;
                margin-top: 2px; /* 减少间距 */
              }
            }

            .feature-list {
              display: none; /* 隐藏特性列表 */
            }
          }
        }

        .login-form-section {
          padding: 8px 16px 16px; /* 极大减少内边距 */

          .login-card {
            .form-tabs {
              margin-bottom: 8px; /* 减少间距 */

              .tab-btn {
                padding: 4px 8px; /* 减少内边距 */
                font-size: 12px;
              }
            }

            .card-header {
              margin-bottom: 8px; /* 减少间距 */

              .login-title {
                font-size: 16px; /* 减小字体 */
              }

              .login-subtitle {
                font-size: 11px;
              }
            }

            .modern-login-form {
              .form-item {
                margin-bottom: 8px; /* 极大减少间距 */

                .input-wrapper {
                  :deep(.el-input) {
                    .el-input__wrapper {
                      .el-input__inner {
                        height: 34px; /* 极大减少高度 */
                        font-size: 12px;
                        padding: 0 10px;
                      }
                    }
                  }
                }
              }

              .modern-login-btn {
                height: 38px; /* 极大减少高度 */
                font-size: 13px;
              }
            }

            .login-footer {
              margin-top: 12px; /* 减少间距 */

              .footer-links {
                flex-direction: column;
                gap: 4px; /* 减少间距 */

                .footer-link {
                  font-size: 11px;
                }
              }
            }
          }
        }
      }
    }
  }
}


</style>
