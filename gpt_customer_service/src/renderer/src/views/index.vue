<template>
  <div class="modern-workspace">
    <div class="workspace-container">
      <!-- 🎨 现代化侧边栏 -->
      <div class="modern-sidebar">
        <div class="sidebar-header">
          <div class="brand-section">
            <div class="brand-icon">
              <div class="ai-logo">
                <div class="logo-circle"></div>
                <div class="logo-pulse"></div>
              </div>
            </div>
            <div class="brand-info">
              <h2 class="brand-title gradient-text">AI智能客服</h2>
              <p class="brand-subtitle">Professional Customer Service</p>
            </div>
          </div>
        </div>

        <nav class="sidebar-nav">
          <div class="nav-section">
            <div class="nav-section-title">主要功能</div>
            <div class="nav-items">
              <router-link
                to="/main/index/home"
                class="nav-item"
                :class="{ active: $route.path === '/main/index/home' }"
              >
                <div class="nav-icon">
                  <el-icon><House /></el-icon>
                </div>
                <span class="nav-text">仪表板</span>
                <div class="nav-indicator"></div>
              </router-link>

              <router-link
                to="/main/index/shop"
                class="nav-item"
                :class="{ active: $route.path === '/main/index/shop' }"
              >
                <div class="nav-icon">
                  <el-icon><Shop /></el-icon>
                </div>
                <span class="nav-text">店铺管理</span>
                <div class="nav-indicator"></div>
              </router-link>

              <router-link
                to="/main/index/knowledge"
                class="nav-item"
                :class="{ active: $route.path === '/main/index/knowledge' }"
              >
                <div class="nav-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <span class="nav-text">知识库</span>
                <div class="nav-indicator"></div>
              </router-link>
            </div>
          </div>

          <div class="nav-section">
            <div class="nav-section-title">系统管理</div>
            <div class="nav-items">
              <router-link
                to="/main/index/setting"
                class="nav-item"
                :class="{ active: $route.path === '/main/index/setting' }"
              >
                <div class="nav-icon">
                  <el-icon><Setting /></el-icon>
                </div>
                <span class="nav-text">系统设置</span>
                <div class="nav-indicator"></div>
              </router-link>
            </div>
          </div>
        </nav>

        <!-- 🎯 侧边栏底部用户区域 -->
        <div class="sidebar-footer">
          <div class="user-profile">
            <!-- 用户头像和信息 -->
            <div class="user-info" @click="showUserMenu = !showUserMenu">
              <div class="user-avatar">
                <el-avatar :size="36" :src="userInfo.avatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <div class="online-indicator"></div>
              </div>
              <div class="user-details">
                <div class="user-name">{{ userInfo.name }}</div>
                <div class="user-role">{{ userInfo.role }}</div>
              </div>
              <div class="user-menu-trigger">
                <el-icon><ArrowUp v-if="showUserMenu" /><ArrowDown v-else /></el-icon>
              </div>
            </div>

            <!-- 用户菜单 -->
            <div v-show="showUserMenu" class="user-menu">
              <div class="menu-item" @click="showMemberUpgradeDialog = true">
                <el-icon><Star /></el-icon>
                <span>会员升级</span>
              </div>
              <div class="menu-item" @click="showPasswordDialog = true">
                <el-icon><Lock /></el-icon>
                <span>修改密码</span>
              </div>
              <div class="menu-item" @click="handleLogout">
                <el-icon><SwitchButton /></el-icon>
                <span>退出登录</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 🎯 主内容区域 -->
      <div class="content-area">
        <router-view v-slot="{ Component }">
          <transition name="page-transition" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </div>

    <!-- 🎯 全局对话框 -->
    <!-- 会员升级对话框 -->
    <el-dialog
      v-model="showMemberUpgradeDialog"
      title="会员升级"
      width="800px"
      :modal="true"
      center
      class="member-upgrade-dialog"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div class="member-upgrade-content">
        <div class="current-member-info">
          <div class="member-status">
            <span class="member-label">当前会员等级</span>
            <span class="member-level">{{ userInfo.memberLevel }}</span>
          </div>
          <div class="member-expire">
            <span class="expire-label">到期时间</span>
            <span class="expire-date">{{ userInfo.memberExpire }}</span>
          </div>
        </div>

        <div class="member-plans">
          <div class="plan-title">选择升级套餐</div>
          <div class="plans-grid">
            <div
              v-for="plan in memberPlans"
              :key="plan.id"
              :class="['plan-card', { selected: selectedPlan === plan.id }]"
              @click="selectedPlan = plan.id"
            >
              <div class="plan-header">
                <div class="plan-name">{{ plan.name }}</div>
                <div class="plan-price">¥{{ plan.price }}</div>
              </div>
              <div class="plan-duration">{{ plan.duration }}</div>
              <div class="plan-features">
                <div v-for="feature in plan.features" :key="feature" class="feature-item">
                  <el-icon><Check /></el-icon>
                  <span>{{ feature }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showMemberUpgradeDialog = false" size="large">取消</el-button>
          <el-button type="primary" @click="handleMemberUpgrade" size="large" :disabled="!selectedPlan">确认升级</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="showPasswordDialog"
      title="修改密码"
      width="450px"
      :modal="true"
      center
      class="password-dialog"
    >
      <div class="password-content">
        <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="100px">
          <el-form-item label="当前密码" prop="currentPassword">
            <el-input
              v-model="passwordForm.currentPassword"
              placeholder="请输入当前密码"
              type="password"
              show-password
              size="large"
            />
          </el-form-item>

          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="passwordForm.newPassword"
              placeholder="请输入新密码"
              type="password"
              show-password
              size="large"
            />
          </el-form-item>

          <el-form-item label="确认新密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              placeholder="请再次输入新密码"
              type="password"
              show-password
              size="large"
            />
          </el-form-item>
        </el-form>

        <div class="password-tips">
          <div class="tips-title">密码要求：</div>
          <ul class="tips-list">
            <li>密码长度至少8位</li>
            <li>包含大小写字母、数字</li>
            <li>建议包含特殊字符</li>
          </ul>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showPasswordDialog = false" size="large">取消</el-button>
          <el-button type="primary" @click="handlePasswordChange" size="large">确认修改</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import {
  User,
  Star,
  Lock,
  ArrowUp,
  ArrowDown,
  SwitchButton,
  Check
} from '@element-plus/icons-vue';
import avatarImg from '@renderer/assets/icon/avatar.png';

// 路由
const router = useRouter();

// 用户信息
const userInfo = ref({
  name: 'AI客服管理员',
  role: '系统管理员',
  avatar: avatarImg,
  balance: 1288.50,
  memberLevel: '普通会员',
  memberExpire: '2024-12-31'
});

// 用户菜单
const showUserMenu = ref(false);

// 对话框状态
const showMemberUpgradeDialog = ref(false);
const showPasswordDialog = ref(false);

// 会员升级相关
const selectedPlan = ref(null);
const memberPlans = ref([
  {
    id: 1,
    name: '高级会员',
    price: 99,
    duration: '1个月',
    features: ['无限对话次数', '优先客服支持', '高级数据分析', '自定义模板']
  },
  {
    id: 2,
    name: '专业会员',
    price: 299,
    duration: '3个月',
    features: ['无限对话次数', '优先客服支持', '高级数据分析', '自定义模板', 'API接口调用', '专属客服经理']
  },
  {
    id: 3,
    name: '企业会员',
    price: 999,
    duration: '1年',
    features: ['无限对话次数', '优先客服支持', '高级数据分析', '自定义模板', 'API接口调用', '专属客服经理', '定制化服务', '技术支持']
  }
]);

// 修改密码相关
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const passwordFormRef = ref();

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度至少8位', trigger: 'blur' },
    { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, message: '密码必须包含大小写字母和数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
};

// 处理会员升级
const handleMemberUpgrade = () => {
  if (!selectedPlan.value) {
    ElMessage.warning('请选择升级套餐');
    return;
  }

  const plan = memberPlans.value.find(p => p.id === selectedPlan.value);
  if (!plan) {
    ElMessage.error('套餐信息错误');
    return;
  }

  // 这里应该调用实际的会员升级API
  ElMessage.success(`成功升级到${plan.name}！`);
  userInfo.value.memberLevel = plan.name;

  // 计算新的到期时间
  const currentDate = new Date();
  if (plan.duration.includes('月')) {
    const months = parseInt(plan.duration);
    currentDate.setMonth(currentDate.getMonth() + months);
  } else if (plan.duration.includes('年')) {
    const years = parseInt(plan.duration);
    currentDate.setFullYear(currentDate.getFullYear() + years);
  }
  userInfo.value.memberExpire = currentDate.toISOString().split('T')[0];

  showMemberUpgradeDialog.value = false;
  selectedPlan.value = null;
  showUserMenu.value = false;
};

// 处理密码修改
const handlePasswordChange = async () => {
  if (!passwordFormRef.value) return;

  try {
    const valid = await passwordFormRef.value.validate();
    if (!valid) return;

    // 这里应该调用实际的密码修改API
    ElMessage.success('密码修改成功！');
    showPasswordDialog.value = false;

    // 清空表单
    passwordForm.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    };
    passwordFormRef.value.clearValidate();
    showUserMenu.value = false;
  } catch (error) {
    console.error('密码修改验证失败:', error);
  }
};

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '退出确认', {
      type: 'warning',
      confirmButtonText: '确定退出',
      cancelButtonText: '取消'
    });

    // 这里应该调用实际的退出登录API
    ElMessage.success('已退出登录');

    // 清除用户数据
    localStorage.removeItem('userToken');
    localStorage.removeItem('userInfo');

    // 跳转到登录页面
    router.push('/login');
  } catch (error) {
    // 用户取消退出
  }
  showUserMenu.value = false;
};

onMounted(() => {
  // 可以在这里获取用户信息
  // 点击其他地方关闭用户菜单
  document.addEventListener('click', (e) => {
    const userProfile = document.querySelector('.user-profile');
    if (userProfile && !userProfile.contains(e.target as Node)) {
      showUserMenu.value = false;
    }
  });
})

</script>

<style lang="scss" scoped>
/* 🎨 现代化工作台样式 */
.modern-workspace {
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.workspace-container {
  display: flex;
  height: 100%;
}

/* 🎯 现代化侧边栏 - 向上移动版 */
.modern-sidebar {
  width: 240px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(102, 126, 234, 0.1);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.05),
    0 2px 6px rgba(102, 126, 234, 0.08);
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1001; /* 确保在顶部导航栏之上 */

  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
    opacity: 0.4;
  }
}

/* 🏢 侧边栏头部 - 优化版 */
.sidebar-header {
  padding: 20px 18px;
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 18px;
    right: 18px;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #667eea 50%, transparent 100%);
    opacity: 0.5;
  }
}

.brand-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.brand-icon {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-logo {
  position: relative;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-circle {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 4px 12px rgba(102, 126, 234, 0.25),
    0 2px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;

  &::before {
    content: '🤖';
    font-size: 18px;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow:
      0 6px 16px rgba(102, 126, 234, 0.3),
      0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

.logo-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 36px;
  height: 36px;
  border: 2px solid #667eea;
  border-radius: 50%;
  animation: pulse-ring 3s infinite;
  z-index: 1;
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.9);
    opacity: 0.7;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.4);
    opacity: 0;
  }
}

.brand-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  text-align: left;
}

.brand-title {
  font-size: 18px;
  font-weight: 700;
  margin: 0;
  line-height: 1.3;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.3px;
}

.brand-subtitle {
  font-size: 9px;
  color: #64748b;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  opacity: 0.8;
}

/* 🧭 导航区域 - 优化版 */
.sidebar-nav {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 24px;

  .nav-section-title {
    font-size: 11px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    padding: 0 18px;
    margin-bottom: 8px;
    opacity: 0.8;
  }

  .nav-items {
    display: flex;
    flex-direction: column;
    gap: 2px;
    padding: 0 12px;
  }
}

.nav-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: 12px var(--spacing-md);
  border-radius: var(--border-radius-md);
  text-decoration: none;
  color: var(--text-secondary);
  transition: all 0.3s var(--ease-out-cubic);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: var(--primary-gradient);
    transition: width 0.3s var(--ease-out-cubic);
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
  }

  .nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.3s var(--ease-out-cubic);
    position: relative;
    z-index: 1;
  }

  .nav-text {
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s var(--ease-out-cubic);
    position: relative;
    z-index: 1;
  }

  .nav-indicator {
    position: absolute;
    right: var(--spacing-md);
    width: 6px;
    height: 6px;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s var(--ease-out-cubic);
  }

  &:hover {
    background: rgba(102, 126, 234, 0.08);
    color: var(--text-primary);
    transform: translateX(4px);

    &::before {
      width: 3px;
    }

    .nav-icon {
      transform: scale(1.1);
      color: var(--primary-color);
    }
  }

  &.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.25);

    &::before {
      width: 100%;
    }

    .nav-icon {
      color: white;
      transform: scale(1.1);
    }

    .nav-text {
      font-weight: 600;
    }

    .nav-indicator {
      opacity: 1;
      transform: scale(1);
      background: white;
    }
  }
}

/* 🎯 侧边栏底部用户区域 */
.sidebar-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.03), rgba(118, 75, 162, 0.03));
}

.user-profile {
  position: relative;

  .user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all 0.3s var(--ease-out-cubic);
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(226, 232, 240, 0.8);

    &:hover {
      background: rgba(255, 255, 255, 0.8);
      border-color: var(--primary-color);
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
    }

    .user-avatar {
      position: relative;
      flex-shrink: 0;

      .online-indicator {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 10px;
        height: 10px;
        background: #10b981;
        border: 2px solid white;
        border-radius: 50%;
        box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.3);
      }
    }

    .user-details {
      flex: 1;
      min-width: 0;

      .user-name {
        font-size: 13px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .user-role {
        font-size: 11px;
        color: var(--text-tertiary);
        font-weight: 500;
      }
    }

    .user-menu-trigger {
      color: var(--text-secondary);
      font-size: 12px;
      transition: transform 0.3s var(--ease-out-cubic);

      .el-icon {
        transition: transform 0.3s var(--ease-out-cubic);
      }
    }
  }

  .user-menu {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--spacing-xs);
    overflow: hidden;
    z-index: 1000;

    .menu-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-sm) var(--spacing-md);
      cursor: pointer;
      transition: all 0.3s var(--ease-out-cubic);
      font-size: 13px;
      color: var(--text-primary);

      &:hover {
        background: rgba(102, 126, 234, 0.05);
        color: var(--primary-color);
      }

      &:not(:last-child) {
        border-bottom: 1px solid rgba(226, 232, 240, 0.5);
      }

      .el-icon {
        font-size: 14px;
        color: var(--text-secondary);
      }

      &:hover .el-icon {
        color: var(--primary-color);
      }
    }
  }
}

/* 🎯 全局对话框样式 */
:deep(.member-upgrade-dialog) {
  .el-dialog {
    max-height: 85vh;
    display: flex;
    flex-direction: column;
    margin-top: 5vh !important;
    margin-bottom: 10vh !important;
  }

  .el-dialog__body {
    flex: 1;
    overflow-y: auto;
    max-height: calc(85vh - 120px);
    padding: var(--spacing-lg) var(--spacing-xl);
  }

  .el-dialog__header {
    flex-shrink: 0;
  }

  .el-dialog__footer {
    flex-shrink: 0;
  }
}

.member-upgrade-content {
  .current-member-info {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
      pointer-events: none;
    }

    .member-status, .member-expire {
      position: relative;
      z-index: 2;
      text-align: center;

      .member-label, .expire-label {
        display: block;
        font-size: 12px;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xs);
        font-weight: 500;
      }

      .member-level, .expire-date {
        font-size: 16px;
        font-weight: 600;
        color: var(--primary-color);
      }
    }
  }

  .member-plans {
    .plan-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: var(--spacing-md);
    }

    .plans-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: var(--spacing-lg);
      align-items: start;

      .plan-card {
        border: 2px solid var(--border-color);
        border-radius: var(--border-radius-lg);
        padding: var(--spacing-lg);
        cursor: pointer;
        transition: all 0.3s var(--ease-out-cubic);
        background: white;
        position: relative;
        overflow: hidden;
        min-height: 320px;
        display: flex;
        flex-direction: column;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
          transform: scaleX(0);
          transition: transform 0.3s var(--ease-out-cubic);
        }

        &:hover {
          border-color: var(--primary-color);
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
          transform: translateY(-4px);

          &::before {
            transform: scaleX(1);
          }
        }

        &.selected {
          border-color: var(--primary-color);
          background: rgba(102, 126, 234, 0.05);
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
          transform: translateY(-2px);

          &::before {
            transform: scaleX(1);
          }

          .plan-header .plan-name {
            color: var(--primary-color);
          }
        }

        .plan-header {
          text-align: center;
          margin-bottom: var(--spacing-md);

          .plan-name {
            font-size: 20px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
            transition: color 0.3s var(--ease-out-cubic);
          }

          .plan-price {
            font-size: 28px;
            font-weight: 800;
            color: var(--primary-color);
            text-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);

            &::before {
              content: '¥';
              font-size: 18px;
              font-weight: 600;
            }
          }
        }

        .plan-duration {
          font-size: 14px;
          color: var(--text-secondary);
          margin-bottom: var(--spacing-lg);
          text-align: center;
          font-weight: 500;
          padding: var(--spacing-xs) var(--spacing-sm);
          background: rgba(102, 126, 234, 0.1);
          border-radius: var(--border-radius-sm);
          display: inline-block;
          width: 100%;
          box-sizing: border-box;
        }

        .plan-features {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;

          .feature-item {
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
            font-size: 13px;
            color: var(--text-primary);
            padding: var(--spacing-xs) 0;
            line-height: 1.4;

            .el-icon {
              color: var(--success-color);
              font-size: 16px;
              flex-shrink: 0;
              margin-top: 2px;
            }

            span {
              line-height: 1.4;
              word-break: break-word;
            }

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

.password-content {
  .password-tips {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    background: rgba(102, 126, 234, 0.05);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--primary-color);

    .tips-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }

    .tips-list {
      margin: 0;
      padding-left: var(--spacing-md);

      li {
        font-size: 13px;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xs);
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  :deep(.el-form) {
    .el-form-item {
      margin-bottom: var(--spacing-lg);

      .el-form-item__label {
        font-weight: 600;
        color: var(--text-primary);
      }

      .el-input {
        .el-input__wrapper {
          border-radius: var(--border-radius-md);
          border: 1.5px solid var(--border-color);
          transition: all 0.3s var(--ease-out-cubic);

          &:hover {
            border-color: var(--primary-color);
          }

          &.is-focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }

          .el-input__inner {
            font-size: 14px;
            padding: 0 var(--spacing-md);
          }
        }
      }
    }
  }
}

/* 🎯 对话框全局样式 */
:deep(.el-dialog) {
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);

  .el-dialog__header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);

    .el-dialog__title {
      font-weight: 700;
      font-size: 18px;
      color: var(--text-primary);
    }

    .el-dialog__headerbtn {
      top: var(--spacing-lg);
      right: var(--spacing-lg);
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.8);
      transition: all 0.3s var(--ease-out-cubic);

      &:hover {
        background: rgba(239, 68, 68, 0.1);
        transform: scale(1.1);
      }

      .el-dialog__close {
        color: var(--text-secondary);
        font-size: 16px;
        font-weight: 600;

        &:hover {
          color: #ef4444;
        }
      }
    }
  }

  .el-dialog__body {
    padding: var(--spacing-xl);
    background: rgba(255, 255, 255, 0.95);
  }

  .el-dialog__footer {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.9), rgba(241, 245, 249, 0.9));
    border-top: 1px solid rgba(226, 232, 240, 0.8);
    padding: var(--spacing-lg) var(--spacing-xl);

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: var(--spacing-md);

      .el-button {
        border-radius: var(--border-radius-md);
        font-weight: 600;
        padding: 12px 24px;
        transition: all 0.3s var(--ease-out-cubic);

        &:hover {
          transform: translateY(-1px);
        }
      }
    }
  }
}

/* 🎯 主内容区域 - 适配固定侧边栏 */
.content-area {
  flex: 1;
  overflow: hidden;
  position: relative;
  margin-left: 240px; /* 为固定侧边栏留出空间 */
}

/* 🎯 响应式设计 */
@media (max-width: 768px) {
  .sidebar-footer {
    padding: var(--spacing-sm);

    .user-profile {
      .user-info {
        padding: var(--spacing-xs);
        gap: var(--spacing-xs);

        .user-details {
          .user-name {
            font-size: 12px;
          }

          .user-role {
            font-size: 10px;
          }
        }

        .user-menu-trigger {
          font-size: 10px;
        }
      }

      .user-menu {
        .menu-item {
          padding: var(--spacing-xs) var(--spacing-sm);
          font-size: 12px;

          .el-icon {
            font-size: 12px;
          }
        }
      }
    }
  }

  /* 移动端会员升级对话框 */
  :deep(.member-upgrade-dialog) {
    .el-dialog {
      width: 95% !important;
      height: 85vh !important;
      margin: 7.5vh auto !important;
    }

    .el-dialog__body {
      max-height: calc(85vh - 140px) !important;
      overflow-y: auto;
    }
  }

  .member-upgrade-content {
    .current-member-info {
      flex-direction: column;
      gap: var(--spacing-md);
      text-align: center;

      .member-status, .member-expire {
        .member-level, .expire-date {
          font-size: 14px;
        }
      }
    }

    .member-plans {
      .plans-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);

        .plan-card {
          padding: var(--spacing-md);
          min-height: 280px;

          .plan-header {
            .plan-name {
              font-size: 18px;
            }

            .plan-price {
              font-size: 24px;
            }
          }

          .plan-features {
            .feature-item {
              font-size: 12px;
              margin-bottom: var(--spacing-xs);
            }
          }
        }
      }
    }
  }

  :deep(.el-dialog) {
    width: 90% !important;
    margin: 5vh auto !important;

    .el-dialog__header {
      padding: var(--spacing-md);

      .el-dialog__title {
        font-size: 16px;
      }
    }

    .el-dialog__body {
      padding: var(--spacing-md);
    }

    .el-dialog__footer {
      padding: var(--spacing-md);

      .dialog-footer {
        .el-button {
          padding: 8px 16px;
          font-size: 13px;
        }
      }
    }
  }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  :deep(.member-upgrade-dialog) {
    .el-dialog {
      width: 90% !important;
      max-width: 700px;
      height: 80vh !important;
      margin-top: 8vh !important;
      margin-bottom: 12vh !important;
    }

    .el-dialog__body {
      max-height: calc(80vh - 140px) !important;
    }
  }

  .member-upgrade-content {
    .member-plans {
      .plans-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);

        .plan-card {
          min-height: 300px;
        }
      }
    }
  }
}

/* 大屏幕优化 */
@media (min-width: 1025px) {
  :deep(.member-upgrade-dialog) {
    .el-dialog {
      width: 900px !important;
      max-width: 90vw;
      height: 75vh !important;
      margin-top: 8vh !important;
      margin-bottom: 17vh !important;
    }

    .el-dialog__body {
      max-height: calc(75vh - 140px) !important;
    }
  }

  .member-upgrade-content {
    .member-plans {
      .plans-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--spacing-xl);

        .plan-card {
          min-height: 350px;
        }
      }
    }
  }
}
</style>
