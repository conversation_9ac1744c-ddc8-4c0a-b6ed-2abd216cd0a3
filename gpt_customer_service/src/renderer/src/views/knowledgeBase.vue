<template>
  <div class="knowledge-base-page">
    <!-- 🎨 现代化知识库界面 -->
    <div class="knowledge-header">
      <div class="header-content">
        <h1 class="page-title gradient-text">知识库管理</h1>
        <p class="page-subtitle">管理AI训练数据和知识内容</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加知识库
        </el-button>
      </div>
    </div>

    <!-- 📊 知识库列表 -->
    <div class="knowledge-content">
      <div class="knowledge-grid">
        <div
          v-for="kb in knowledgeBases"
          :key="kb.id"
          class="knowledge-card modern-card hover-lift"
        >
          <div class="card-header">
            <div class="kb-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="kb-info">
              <h3 class="kb-name">{{ kb.name }}</h3>
              <p class="kb-desc">{{ kb.description || '暂无描述' }}</p>
            </div>
          </div>
          <div class="card-stats">
            <div class="stat-item">
              <span class="stat-label">文档数量</span>
              <span class="stat-value">{{ kb.document_count || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">更新时间</span>
              <span class="stat-value">{{ formatDate(kb.updated_at) }}</span>
            </div>
          </div>
          <div class="card-actions">
            <el-button size="small" @click="editKnowledgeBase(kb)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteKnowledgeBase(kb.id)">删除</el-button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="knowledgeBases.length === 0" class="empty-state">
          <el-empty description="暂无知识库">
            <template #image>
              <el-icon class="empty-icon"><Document /></el-icon>
            </template>
            <el-button type="primary" @click="showAddDialog = true">创建第一个知识库</el-button>
          </el-empty>
        </div>
      </div>
    </div>

    <!-- 添加/编辑知识库对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="isEdit ? '编辑知识库' : '添加知识库'"
      width="500px"
    >
      <el-form :model="kbForm" label-width="80px">
        <el-form-item label="名称">
          <el-input v-model="kbForm.name" placeholder="请输入知识库名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="kbForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入知识库描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveKnowledgeBase">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Document } from '@element-plus/icons-vue'

// 知识库数据
const knowledgeBases = ref<any[]>([])
const showAddDialog = ref(false)
const isEdit = ref(false)
const kbForm = ref({
  id: '',
  name: '',
  description: ''
})

// 获取知识库列表
const getKnowledgeBases = async () => {
  try {
    const res = await window.api.getKnowledgeBasesList()
    knowledgeBases.value = res.data || []
  } catch (error) {
    console.error('获取知识库列表失败:', error)
    ElMessage.error('获取知识库列表失败')
  }
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '未知'
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 编辑知识库
const editKnowledgeBase = (kb: any) => {
  isEdit.value = true
  kbForm.value = {
    id: kb.id,
    name: kb.name,
    description: kb.description || ''
  }
  showAddDialog.value = true
}

// 保存知识库
const saveKnowledgeBase = async () => {
  if (!kbForm.value.name.trim()) {
    ElMessage.warning('请输入知识库名称')
    return
  }

  try {
    // 这里应该调用实际的API
    ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
    showAddDialog.value = false
    resetForm()
    getKnowledgeBases()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  }
}

// 删除知识库
const deleteKnowledgeBase = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这个知识库吗？', '确认删除', {
      type: 'warning'
    })

    // 这里应该调用实际的删除API
    ElMessage.success('删除成功')
    getKnowledgeBases()
  } catch (error) {
    // 用户取消删除
  }
}

// 重置表单
const resetForm = () => {
  isEdit.value = false
  kbForm.value = {
    id: '',
    name: '',
    description: ''
  }
}

onMounted(() => {
  getKnowledgeBases()
})
</script>

<style lang="scss" scoped>
/* 🎨 现代化知识库样式 */
.knowledge-base-page {
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100%;
  overflow-y: auto;
}

.knowledge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);

  .header-content {
    .page-title {
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 8px;
      background: var(--primary-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .page-subtitle {
      color: var(--text-secondary);
      font-size: 14px;
      margin: 0;
    }
  }

  .header-actions {
    .el-button {
      border-radius: var(--border-radius-md);
      box-shadow: var(--shadow-sm);
    }
  }
}

.knowledge-content {
  .knowledge-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
  }

  .knowledge-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s var(--ease-out-cubic);

    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-xl);
      border-color: var(--primary-color);
    }

    .card-header {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-md);

      .kb-icon {
        width: 48px;
        height: 48px;
        background: var(--primary-gradient);
        border-radius: var(--border-radius-md);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
        flex-shrink: 0;
      }

      .kb-info {
        flex: 1;

        .kb-name {
          font-size: 18px;
          font-weight: 600;
          color: var(--text-primary);
          margin: 0 0 4px 0;
        }

        .kb-desc {
          color: var(--text-secondary);
          font-size: 14px;
          margin: 0;
          line-height: 1.4;
        }
      }
    }

    .card-stats {
      display: flex;
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-md);
      padding: var(--spacing-md);
      background: rgba(248, 250, 252, 0.8);
      border-radius: var(--border-radius-md);

      .stat-item {
        flex: 1;
        text-align: center;

        .stat-label {
          display: block;
          font-size: 12px;
          color: var(--text-tertiary);
          margin-bottom: 4px;
        }

        .stat-value {
          display: block;
          font-size: 16px;
          font-weight: 600;
          color: var(--text-primary);
        }
      }
    }

    .card-actions {
      display: flex;
      gap: var(--spacing-sm);
      justify-content: flex-end;

      .el-button {
        border-radius: var(--border-radius-sm);
      }
    }
  }

  .empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-xl);

    .empty-icon {
      font-size: 64px;
      color: var(--text-tertiary);
      margin-bottom: var(--spacing-md);
    }

    .el-button {
      margin-top: var(--spacing-md);
      border-radius: var(--border-radius-md);
    }
  }
}

/* 🎯 对话框样式 */
:deep(.el-dialog) {
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);

  .el-dialog__header {
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    padding: var(--spacing-lg);

    .el-dialog__title {
      font-weight: 600;
      color: var(--text-primary);
    }
  }

  .el-dialog__body {
    padding: var(--spacing-lg);
  }

  .el-dialog__footer {
    border-top: 1px solid rgba(226, 232, 240, 0.8);
    padding: var(--spacing-lg);

    .el-button {
      border-radius: var(--border-radius-md);
    }
  }
}

/* 🎯 响应式设计 */
@media (max-width: 768px) {
  .knowledge-base-page {
    padding: var(--spacing-md);
  }

  .knowledge-header {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .knowledge-content .knowledge-grid {
    grid-template-columns: 1fr;
  }
}
</style>
