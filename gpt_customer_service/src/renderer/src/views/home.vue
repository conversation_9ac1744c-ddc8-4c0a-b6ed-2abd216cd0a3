<template>
  <div class="modern-home">
    <!-- 🎯 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="welcome-text">
          <h1 class="welcome-title gradient-text">欢迎使用AI智能客服系统</h1>
          <p class="welcome-subtitle">高效、智能、专业的客户服务解决方案</p>
        </div>
        <div class="banner-stats">
          <div class="stat-item">
            <div class="stat-number">{{ stats.totalMessages }}</div>
            <div class="stat-label">今日消息</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.activeUsers }}</div>
            <div class="stat-label">在线用户</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.responseTime }}ms</div>
            <div class="stat-label">响应时间</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 📊 数据概览卡片 -->
    <div class="dashboard-grid">
      <div class="dashboard-card modern-card floating-3d">
        <div class="card-header">
          <div class="card-icon success">
            <el-icon><User /></el-icon>
          </div>
          <div class="card-info">
            <h3>咨询人数</h3>
            <p class="card-value">{{ stats.consultationUsers }}</p>
            <span class="card-trend positive">+12.5%</span>
          </div>
        </div>
        <div class="card-chart">
          <div class="mini-chart">
            <div class="chart-bar" v-for="(value, index) in chartData.users" :key="index"
                 :style="{ height: value + '%' }"></div>
          </div>
        </div>
      </div>

      <div class="dashboard-card modern-card floating-3d">
        <div class="card-header">
          <div class="card-icon warning">
            <el-icon><ChatDotRound /></el-icon>
          </div>
          <div class="card-info">
            <h3>消息量</h3>
            <p class="card-value">{{ stats.messageCount }}</p>
            <span class="card-trend negative">-3.2%</span>
          </div>
        </div>
        <div class="card-chart">
          <div class="mini-chart">
            <div class="chart-bar" v-for="(value, index) in chartData.messages" :key="index"
                 :style="{ height: value + '%' }"></div>
          </div>
        </div>
      </div>

      <div class="dashboard-card modern-card floating-3d">
        <div class="card-header">
          <div class="card-icon primary">
            <el-icon><QuestionFilled /></el-icon>
          </div>
          <div class="card-info">
            <h3>高频问题</h3>
            <p class="card-value">{{ stats.frequentQuestions }}</p>
            <span class="card-trend positive">+5.8%</span>
          </div>
        </div>
        <div class="card-chart">
          <div class="satisfaction-ring">
            <div class="ring-progress" :style="{ '--progress': stats.frequentQuestions + '%' }"></div>
            <div class="ring-center">{{ stats.frequentQuestions }}</div>
          </div>
        </div>
      </div>

      <div class="dashboard-card modern-card floating-3d">
        <div class="card-header">
          <div class="card-icon error">
            <el-icon><ShoppingCart /></el-icon>
          </div>
          <div class="card-info">
            <h3>产品咨询量</h3>
            <p class="card-value">{{ stats.productConsultations }}</p>
            <span class="card-trend neutral">0%</span>
          </div>
        </div>
        <div class="card-actions">
          <button class="modern-btn modern-btn-primary">查看详情</button>
        </div>
      </div>
    </div>

    <!-- 🎯 快速操作区域 -->
    <div class="quick-actions">
      <h2 class="section-title">快速操作</h2>
      <div class="actions-grid">
        <div class="action-card modern-card hover-lift" @click="navigateToReception">
          <div class="action-icon">
            <el-icon><ChatDotRound /></el-icon>
          </div>
          <h3>进入接待中心</h3>
          <p>开始处理客户咨询</p>
        </div>

        <div class="action-card modern-card hover-lift" @click="navigateToKnowledge">
          <div class="action-icon">
            <el-icon><Files /></el-icon>
          </div>
          <h3>知识库管理</h3>
          <p>管理AI训练数据</p>
        </div>

        <div class="action-card modern-card hover-lift" @click="navigateToSettings">
          <div class="action-icon">
            <el-icon><Setting /></el-icon>
          </div>
          <h3>系统设置</h3>
          <p>配置系统参数</p>
        </div>

        <div class="action-card modern-card hover-lift" @click="navigateToShop">
          <div class="action-icon">
            <el-icon><Shop /></el-icon>
          </div>
          <h3>店铺管理</h3>
          <p>管理接入平台</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  User,
  ChatDotRound,
  QuestionFilled,
  ShoppingCart,
  Files,
  Setting,
  Shop
} from '@element-plus/icons-vue';

const router = useRouter();

// 统计数据
const stats = ref({
  totalMessages: 1247,
  activeUsers: 23,
  responseTime: 156,
  consultationUsers: 892,
  messageCount: 2347,
  frequentQuestions: 15,
  productConsultations: 156
});

// 图表数据
const chartData = ref({
  users: [65, 78, 82, 95, 88, 76, 89, 92],
  messages: [45, 52, 48, 61, 55, 67, 59, 63]
});

// 导航函数
const navigateToReception = () => {
  router.push('/main/reception');
};

const navigateToKnowledge = () => {
  router.push('/main/index/knowledge');
};

const navigateToSettings = () => {
  router.push('/main/index/setting');
};

const navigateToShop = () => {
  router.push('/main/index/shop');
};

// 模拟数据更新
const updateStats = () => {
  stats.value.totalMessages += Math.floor(Math.random() * 5);
  stats.value.activeUsers = 20 + Math.floor(Math.random() * 10);
  stats.value.responseTime = 150 + Math.floor(Math.random() * 20);
};

onMounted(() => {
  // 每30秒更新一次数据
  setInterval(updateStats, 30000);
});
</script>

<style lang="scss" scoped>
.modern-home {
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100%;
  overflow-y: auto;
}

/* 🎯 欢迎横幅 */
.welcome-banner {
  background: var(--primary-gradient);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-2xl);
  margin-bottom: var(--spacing-xl);
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-xl);

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
  }

  .banner-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .welcome-text {
    flex: 1;
  }

  .welcome-title {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 var(--spacing-sm) 0;
    background: linear-gradient(45deg, #ffffff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .welcome-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
  }

  .banner-stats {
    display: flex;
    gap: var(--spacing-xl);
  }

  .stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-md);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .stat-number {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 4px;
  }

  .stat-label {
    font-size: 12px;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 📊 仪表板网格 */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.dashboard-card {
  padding: var(--spacing-lg);

  .card-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }

  .card-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;

    &.success { background: var(--success-color); }
    &.warning { background: var(--warning-color); }
    &.primary { background: var(--primary-color); }
    &.error { background: var(--error-color); }
  }

  .card-info {
    flex: 1;

    h3 {
      margin: 0 0 4px 0;
      font-size: 14px;
      color: var(--text-secondary);
      font-weight: 500;
    }

    .card-value {
      margin: 0 0 4px 0;
      font-size: 24px;
      font-weight: 700;
      color: var(--text-primary);
    }

    .card-trend {
      font-size: 12px;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 4px;

      &.positive {
        color: var(--success-color);
        background: rgba(16, 185, 129, 0.1);
      }

      &.negative {
        color: var(--error-color);
        background: rgba(239, 68, 68, 0.1);
      }

      &.neutral {
        color: var(--text-secondary);
        background: var(--bg-tertiary);
      }
    }
  }

  .card-chart {
    margin-top: var(--spacing-md);
  }

  .mini-chart {
    display: flex;
    align-items: end;
    gap: 2px;
    height: 40px;

    .chart-bar {
      flex: 1;
      background: var(--primary-gradient);
      border-radius: 2px;
      min-height: 4px;
      transition: height 0.3s var(--ease-out-cubic);
    }
  }

  .satisfaction-ring {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto;

    .ring-progress {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      background: conic-gradient(var(--primary-color) var(--progress), var(--bg-tertiary) 0);
      display: flex;
      align-items: center;
      justify-content: center;

      &::before {
        content: '';
        width: 70%;
        height: 70%;
        background: white;
        border-radius: 50%;
      }
    }

    .ring-center {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 12px;
      font-weight: 600;
      color: var(--text-primary);
    }
  }

  .card-actions {
    margin-top: var(--spacing-md);
    text-align: center;
  }
}

/* 🎯 快速操作区域 */
.quick-actions {
  .section-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-lg) 0;
    text-align: center;
  }

  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--spacing-lg);
  }

  .action-card {
    padding: var(--spacing-xl);
    text-align: center;
    cursor: pointer;
    transition: all 0.3s var(--ease-out-cubic);
    border: 2px solid transparent;

    &:hover {
      border-color: var(--primary-color);
      transform: translateY(-8px);
      box-shadow: var(--shadow-2xl);
    }

    .action-icon {
      width: 64px;
      height: 64px;
      margin: 0 auto var(--spacing-md) auto;
      background: var(--primary-gradient);
      border-radius: var(--border-radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
      color: white;
      box-shadow: var(--shadow-lg);
      transition: transform 0.3s var(--ease-out-cubic);
    }

    &:hover .action-icon {
      transform: scale(1.1) rotate(5deg);
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 var(--spacing-sm) 0;
    }

    p {
      font-size: 14px;
      color: var(--text-secondary);
      margin: 0;
      line-height: 1.5;
    }
  }
}

/* 🎭 响应式设计 */
@media (max-width: 768px) {
  .welcome-banner .banner-content {
    flex-direction: column;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .banner-stats {
    justify-content: center;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}
</style>
