<template>
  <div class="shop-page">
    <div class="header-title">
      <h1>店铺列表</h1>
      <el-dropdown>
        <button class="add-shop"><el-icon>
            <Plus />
          </el-icon> 店铺</button>
        <template #dropdown>
          <div class="shop-list-box">
            <ul class="shop-list">
              <li v-for="shop in shopList" :key="shop.id" class="shop-item" @click="openNewView(shop.id)">
                <el-image :src="shopIcon[shop.name]">
                  <template #error>
                    <div class="image-slot">
                      <el-icon>
                        <Picture />
                      </el-icon>
                    </div>
                  </template>
                </el-image>
                <span class="shop-name">{{ shop.name }}</span>
              </li>
            </ul>
          </div>
        </template>
      </el-dropdown>
    </div>

    <el-table :data="viewList" style="width: 100%">
      <el-table-column prop="Shop.icon" label="店铺" width="60">
        <template #default="scope">
          <img style="width: 100%;" :src="shopIcon[scope.row.Shop.name]" :alt="scope.row.Shop.name">
        </template>
      </el-table-column>
      <el-table-column prop="name" label="" />
      <el-table-column prop="loginState" label="登录状态" width="100" align="center">
        <template #default="scope">
          <span v-if="scope.row.loginState === 1" style="color: #67C23A">已登录</span>
          <el-button v-else type="primary" @click="toLogin(scope.row.viewId)">去登录</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="collectionState" label="采集产品" width="120" align="center">
        <template #default="scope">
          <el-button :disabled="scope.row.loginState != 1" type="primary" @click="startCollect(scope.row)"
            :loading="scope.row.loginState === 1 && scope.row.collectionState === 1">{{ scope.row.collectionState === 2 ? '重新采集' : '开始采集'
            }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="isDuty" label="接待" width="80">
        <template #default="scope">
          <el-switch :disabled="scope.row.loginState === 0" v-model="scope.row.isDuty" inline-prompt :active-value="1"
            :inactive-value="0" style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949" active-text="值班"
            inactive-text="休息" @change="changeDuty(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button type="primary" @click="openNewView(scope.row.viewId, true)">编辑</el-button>
          <el-button type="danger" @click="deleteView(scope.row.viewId)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增视图 -->
    <el-dialog v-model="addDialogVisible" :title="isEdit ? '编辑店铺平台' : '新增店铺平台'" width="500" :before-close="handleClose">
      <div>
        <el-form labelWidth="auto">
          <el-form-item label="店铺名称">
            <el-input v-model="shopName" placeholder="请输入名称" />
          </el-form-item>
          <el-form-item label="知识库" aria-placeholder="请选择知识库">
            <el-select v-model="selectedKnowledgeBase">
              <el-option v-for="item in knowledgeBases" :label="item.kb_name + (item.kb_info ? ':' + item.kb_info : '')"
                :value="item.kb_name"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客服模板" aria-placeholder="请选择客服模板">
            <el-select v-model="selectedPrompTemplate">
              <el-option v-for="item in promptTemplateList"
                :label="item.prompt_name + (item.description ? ':' + item.description : '')"
                :value="item.prompt_name"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirm">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElSelect, ElOption, ElSwitch, ElIcon, ElImage, ElFormItem, ElForm, ElInput } from 'element-plus'
import { useRouter } from 'vue-router';
import doudian from '@renderer/assets/icon/doudian.png'
import kuaishouxiaodian from '@renderer/assets/icon/kuaishouxiaodian.png'
import qianniu from '@renderer/assets/icon/qianniu.png'
import weixinxiaodian from '@renderer/assets/icon/weixinxiaodian.png'
import xiaohongshu from '@renderer/assets/icon/xiaohongshu.png'
import pingduoduo from '@renderer/assets/icon/pingduoduo.png'

const shopIcon = {
  "抖店飞鸽": doudian,
  "千牛天猫": qianniu,
  "小红书千帆": xiaohongshu,
  "拼多多": pingduoduo,
  "微信小店": weixinxiaodian,
  "快手小店": kuaishouxiaodian,
}


const router = useRouter();

// 新增店铺
const shopList = ref<any>([])
function getShopList() {
  window.api.getShop().then((res: any) => {
    shopList.value = res
  })
}

// 删除店铺
function deleteView(viewId) {
  ElMessageBox.confirm(
    '确定要删除吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    window.api.closeView(viewId).then(() => {
      getViewList()
    })
  }).catch(() => { })
}


// 打开新窗口
const selectedKnowledgeBase = ref("");
const selectedPrompTemplate = ref("");
const shopName = ref("");
const userName = ref("");
const password = ref("");

const addDialogVisible = ref(false)
const isEdit = ref(false)
const dialogId = ref("")

function openNewView(id: string, edit: boolean = false) {
  window.api.selectView("-1")
  if (knowledgeBases.value.length == 0) {
    getKnowledgeBases()
  }
  if (promptTemplateList.value.length == 0) {
    getPromptTemplateList()
  }
  if (edit) {
    const view = viewList.value.find((item) => item.viewId === id)
    selectedKnowledgeBase.value = view.knowledge
    userName.value = view.userName
    password.value = view.password
    selectedPrompTemplate.value = view.promptName
    if (view.name) {
      shopName.value = view.name
    }
  } else {
    shopName.value = shopList.value.find((item) => item.id === id).name
  }
  dialogId.value = id
  isEdit.value = edit
  addDialogVisible.value = true
}


// 弹窗确认
function confirm() {
  if (selectedKnowledgeBase.value) {
    if (isEdit.value) {
      window.api.updateView(dialogId.value, selectedKnowledgeBase.value, userName.value, password.value, shopName.value, selectedPrompTemplate.value).then(() => {
        getViewList()
        selectedKnowledgeBase.value = ''
        userName.value = ''
        password.value = ''
        shopName.value = ''
        selectedPrompTemplate.value = ''
        addDialogVisible.value = false
      })
    } else {
      window.api.createView({
        shopId: dialogId.value,
        knowledge: selectedKnowledgeBase.value,
        userName: userName.value,
        password: password.value,
        shopName: shopName.value,
        promptName: selectedPrompTemplate.value
      }).then(() => {
        getViewList()
        selectedKnowledgeBase.value = ''
        userName.value = ''
        password.value = ''
        shopName.value = ''
        selectedPrompTemplate.value = ''
        addDialogVisible.value = false
        router.push('/reception?login=1');
      })
    }
  } else {
    ElMessage.error('请选择知识库')
  }
}

function handleClose() {
  userName.value = ''
  password.value = ''
  shopName.value = ''
  selectedKnowledgeBase.value = ''
  selectedPrompTemplate.value = ''
  addDialogVisible.value = false
}




// 获取店铺列表
const viewList = ref<any>([])
function getViewList() {
  window.api.getViews().then((res: any) => {
    viewList.value = res
  })
}

// 获取知识库列表
const knowledgeBases = ref<any>([])
function getKnowledgeBases() {
  window.api.getKnowledgeBasesList().then(res => {
    knowledgeBases.value = res.data;
  })
}

// 获取客服模板
const promptTemplateList = ref<any>([])
const promptTemplateTotal = ref(0)
async function getPromptTemplateList() {
  const res = await window.api.getPromptTemplateList({
    prompt_type: '',
    prompt_name: '',
    page: 1,
    per_page: 49
  })
  promptTemplateList.value = res.data.prompt_list;
  promptTemplateTotal.value = res.data.total;
}

// 修改值班状态
function changeDuty(view) {
  window.api.switchDuty(view.viewId).then(() => {
    getViewList()
  })
}

// 去登录
function toLogin(viewId) {
  router.push('/reception?login=1');
  window.api.selectView(viewId)
}

let getViewTimer: any = NaN

// 启动采集
function startCollect(row) {
  window.api.startCollect(row.viewId)
  if (getViewTimer) clearTimeout(getViewTimer)
  getViewListTimer()
  setTimeout(() => {
    row.collectionState = 1
  }, 1000);
}

// 定时调用获取视图列表
function getViewListTimer() {
  getViewList()
  getViewTimer = setTimeout(() => {
    getViewListTimer()
  }, 5000);
}

onMounted(() => {
  getViewList()
  getShopList()
  getKnowledgeBases()
  getViewListTimer()
  getPromptTemplateList()
})

</script>

<style lang="scss" scoped>
.add-shop {
  width: 80px;
  height: 30px;
  line-height: 30px;
  padding: 0;
  border-radius: 15px;
  border: none;
  background-image: linear-gradient(319deg, rgba(0, 4, 140, 1) 0%, rgba(162, 12, 204, 1) 100%);
  background-repeat: repeat;
  background-size: normal;
  color: #fff;
  cursor: pointer;
  margin-left: 10px;
}

.shop-list-box {
  overflow: hidden;

  .shop-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    padding: 10px;
    margin-right: -10px;

    .shop-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      background-color: #fff;
      border: 1px solid #ccc;
      margin-bottom: 10px;
      width: calc(100% / 3 - 10px);
      margin-bottom: 10px;
      margin-right: 10px;
      aspect-ratio: 1;
      cursor: pointer;

      .el-image {
        width: 40px;
        height: 40px;
        margin-bottom: 5px;
        border-radius: 5px;

        .image-slot {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          background-color: #f0f0f0;

          .el-icon {
            font-size: 20px;
          }
        }
      }

      .shop-name {
        font-size: 14px;
        color: #333;
      }
    }
  }
}
</style>