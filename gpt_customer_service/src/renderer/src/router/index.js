import { createRouter, createWebHashHistory } from "vue-router";
import Layout from '@renderer/layout/modern.vue'

const routes = [
  {
    path: '/login',
    component: () => import('@renderer/views/login.vue'),
  },
  {
    path: '/',
    redirect: '/login', // 默认重定向到登录页面
  },
  {
    path: '/main',
    component: Layout,
    redirect: '/main/index/home',
    children: [
      {
        path: 'index',
        name: 'Index',
        component: () => import('@renderer/views/index.vue'),
        children: [
          {
            path: 'home',
            name: 'Home',
            component: () => import('@renderer/views/home.vue')
          },
          {
            path: 'shop',
            name: 'Shop',
            component: () => import('@renderer/views/shop.vue')
          },
          {
            path: 'knowledge',
            name: 'Knowledge',
            component: () => import('@renderer/views/knowledgeBase.vue')
          },
          {
            path: 'setting',
            name: 'Setting',
            component: () => import('@renderer/views/setting.vue')
          }
        ]
      },
      {
        path: 'reception',
        name: 'Reception',
        component: () => import('@renderer/views/reception.vue')
      },
    ]
  }
]

const router = new createRouter({
  history: createWebHashHistory(),
  routes
})

export default router;
