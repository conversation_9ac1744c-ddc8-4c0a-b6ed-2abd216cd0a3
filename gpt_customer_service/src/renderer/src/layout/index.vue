<template>
  <div class="modern-layout">
    <!-- 🎨 现代化顶部导航栏 -->
    <div class="modern-header">
      <div class="header-content">
        <!-- 左侧品牌和导航 -->
        <div class="left-section">
          <div class="brand-section">
            <div class="brand-icon">
              <div class="ai-logo">
                <div class="logo-circle"></div>
                <div class="logo-pulse"></div>
              </div>
            </div>
            <div class="brand-info">
              <h1 class="brand-title gradient-text">AI智能客服</h1>
              <span class="brand-subtitle">Professional Customer Service</span>
            </div>
          </div>

          <div class="nav-switcher">
            <button
              @click="navigateTo('/index/home')"
              :class="['nav-btn', { active: currentRoute.includes('/index') }]"
            >
              <el-icon class="nav-icon"><House /></el-icon>
              <span>工作台</span>
              <div class="nav-indicator"></div>
            </button>
            <button
              @click="navigateTo('/reception')"
              :class="['nav-btn', { active: currentRoute.includes('/reception') }]"
            >
              <el-icon class="nav-icon"><ChatDotRound /></el-icon>
              <span>接待中心</span>
              <div class="nav-indicator"></div>
            </button>
          </div>
        </div>

        <!-- 右侧系统状态和控制 -->
        <div class="right-section">
          <div class="system-status">
            <div class="status-item">
              <div class="status-indicator status-online">
                <div class="status-dot"></div>
                <span>系统正常</span>
              </div>
            </div>
            <div class="time-display">
              {{ currentTime }}
            </div>
          </div>

          <div class="window-controls">
            <button @click="rightSelect('minimize')" class="control-btn minimize-btn">
              <el-icon><Minus /></el-icon>
            </button>
            <button @click="rightSelect('maximize')" class="control-btn maximize-btn">
              <el-icon>
                <svg v-if="!isMaximized" viewBox="0 0 1024 1024" width="16" height="16">
                  <path d="M213.8 65H674.4c81.6 0 148 66.4 148 148v460.6c0 81.6-66.4 148-148 148H213.8c-81.6 0-148-66.4-148-148V213c0-81.6 66.4-148 148-148z m0 92c-30.9 0-56 25.1-56 56v460.6c0 30.9 25.1 56 56 56H674.4c30.9 0 56-25.1 56-56V213c0-30.9-25.1-56-56-56H213.8z"/>
                </svg>
                <svg v-else viewBox="0 0 1024 1024" width="16" height="16">
                  <path d="M812.2 65H351.6c-78.3 0-142.5 61.1-147.7 138.1-77 5.1-138.1 69.4-138.1 147.7v460.6c0 81.6 66.4 148 148 148h460.6c78.3 0 142.5-61.1 147.7-138.1 77-5.1 138.1-69.4 138.1-147.7V213c0-81.6-66.4-148-148-148z m-45.8 746.3c0 50.7-41.3 92-92 92H213.8c-50.7 0-92-41.3-92-92V350.7c0-50.7 41.3-92 92-92h460.6c50.7 0 92 41.3 92 92v460.6z m137.8-137.7c0 47.3-35.8 86.3-81.8 91.4V350.7c0-81.6-66.4-148-148-148H260.2c5.1-45.9 44.2-81.8 91.4-81.8h460.6c50.7 0 92 41.3 92 92v460.7z"/>
                </svg>
              </el-icon>
            </button>
            <button @click="rightSelect('quit')" class="control-btn close-btn">
              <el-icon><Close /></el-icon>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎯 主内容区域 -->
    <div class="modern-main">
      <router-view v-slot="{ Component }">
        <transition name="page-transition" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

// 当前路由路径
const currentRoute = computed(() => route.path);

// 时间显示
const currentTime = ref('');
let timeInterval: NodeJS.Timeout;

const updateTime = () => {
  const now = new Date();
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 路由导航函数
function navigateTo(path: string) {
  if (path === '/index/home') {
    window.api.selectView("-1");
    if (currentRoute.value.includes('/index')) return;
  }
  router.push(path);
}

// 窗口控制
const isMaximized = ref(false);
function rightSelect(index: string) {
  if (index === 'minimize') {
    window.api.minimize()
  } else if (index === 'maximize') {
    window.api.maximize(isMaximized.value)
    isMaximized.value = !isMaximized.value
  } else if (index === 'quit') {
    window.api.quit()
  }
}

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});

</script>

<style lang="scss" scoped>
/* 🎨 现代化布局样式 */
.modern-layout {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;

}

/* 🎯 现代化头部导航 */
.modern-header {
  height: 70px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
  position: relative;
  z-index: 1000;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-gradient);
  }

  .header-content {
    height: 100%;
    padding: 0 var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;

    * {
      -webkit-app-region: no-drag;
    }
  }

    .el-menu--horizontal {
      --el-menu-horizontal-height: 100%;
      --el-menu-bg-color: transparent;
      --el-menu-text-color: #333;
      --el-menu-active-color: #333;
      --el-menu-hover-bg-color: #00293b;
      --el-menu-hover-text-color: #fff;
      --el-menu-icon-width: 18px;

      &.el-menu {
        align-items: center;
        border-bottom: none;
      }

      .el-menu-item {
        height: 34px;
        border-radius: 8px;
        border-bottom: none;
        overflow: hidden;

        &.is-active {
          background-color: transparent;
        }

        [class^=el-icon] {
          margin-right: 0;
        }
      }
    }

    .left-menu-wrapper {
      flex: 0;
      display: flex;
      align-content: center;
      justify-content: flex-start;
      align-items: center;

      .switch-page {
        display: flex;
        justify-content: space-between;
        width: 170px;
        height: 35px;
        padding: 3px;
        background: linear-gradient(145deg, #2a86ae, #3ba4ce);
        border-radius: 18px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);

        button {
          width: 74px;
          height: 29px;
          border-radius: 18px;
          border: none;
          background: linear-gradient(145deg, #f0f0f0, #d8d8d8);
          box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1), -2px -2px 5px rgba(255, 255, 255, 0.5);
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(145deg, #e8e8e8, #d0d0d0);
            color: #333;
            box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1), -1px -1px 3px rgba(255, 255, 255, 0.7);
          }

          &.active {
            background: linear-gradient(145deg, #1a73e8, #1658b3);
            color: white;
            font-weight: bold;
            box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.2), inset -1px -1px 3px rgba(255, 255, 255, 0.1);
          }
        }
      }

      .user-info {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-right: 20px;

        .user-avatar {
          display: block;
          width: 56px;
          height: 56px;
          border-radius: 50%;
          object-fit: cover;
        }

        .user-name {
          margin-left: 10px;
          font-weight: bold;
        }
      }

      .add-shop {
        width: 80px;
        height: 30px;
        line-height: 30px;
        padding: 0;
        border-radius: 15px;
        border: none;
        background-image: linear-gradient(319deg, rgba(0, 4, 140, 1) 0%, rgba(162, 12, 204, 1) 100%);
        background-repeat: repeat;
        background-size: normal;
        color: #fff;
        cursor: pointer;
      }
    }

    .right-menu-wrapper {
      flex: 0;
      display: flex;
      align-items: center;

      button {
        width: 40px;
        height: 33px;
        background: linear-gradient(145deg, #f8f8f8, #e7e7e7);
        margin-left: 5px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1), -2px -2px 5px rgba(255, 255, 255, 0.7);
        transition: all 0.2s ease;

        &:hover {
          background: linear-gradient(145deg, #e0e0e0, #f0f0f0);
          color: #333;
          box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1), -1px -1px 3px rgba(255, 255, 255, 0.5);
        }

        &:active {
          box-shadow: inset 2px 2px 5px rgba(0, 0, 0, 0.1), inset -1px -1px 3px rgba(255, 255, 255, 0.5);
        }

        &:last-child:hover {
          background: linear-gradient(145deg, #e60000, #cc0000);
          color: #fff;
          box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2), -1px -1px 3px rgba(255, 100, 100, 0.3);
        }
      }

    }

    .right-menu {
      .el-menu-item {
        margin-left: 5px;
        padding: 0 10px;

        &:last-child {
          --el-menu-hover-bg-color: #cc0000;
        }
      }
    }
  }

  .main {
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    overflow: auto;
    background-color: #3194be;
    padding: 5px;
  }
}
</style>
