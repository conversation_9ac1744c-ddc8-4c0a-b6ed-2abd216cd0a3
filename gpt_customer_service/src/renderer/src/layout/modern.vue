<template>
  <div class="modern-layout">
    <!-- 🎨 现代化顶部导航栏 -->
    <div class="modern-header">
      <div class="header-content">
        <!-- 左侧品牌和导航 -->
        <div class="left-section">

          <div class="nav-switcher">
            <button
              @click="navigateTo('/main/index/home')"
              :class="['nav-btn', { active: currentRoute.includes('/main/index') }]"
            >
              <el-icon class="nav-icon"><House /></el-icon>
              <span>工作台</span>
              <div class="nav-indicator"></div>
            </button>
            <button
              @click="navigateTo('/main/reception')"
              :class="['nav-btn', { active: currentRoute.includes('/main/reception') }]"
            >
              <el-icon class="nav-icon"><ChatDotRound /></el-icon>
              <span>接待中心</span>
              <div class="nav-indicator"></div>
            </button>
          </div>
        </div>

        <!-- 右侧系统状态和控制 -->
        <div class="right-section">
          <div class="system-status">
            <div class="status-item">
              <div class="status-indicator status-online">
                <div class="status-dot"></div>
                <span>系统正常</span>
              </div>
            </div>
            <div class="time-display">
              {{ currentTime }}
            </div>
          </div>

          <div class="window-controls">
            <button @click="rightSelect('minimize')" class="control-btn minimize-btn">
              <el-icon><Minus /></el-icon>
            </button>
            <button @click="rightSelect('maximize')" class="control-btn maximize-btn">
              <el-icon>
                <svg v-if="!isMaximized" viewBox="0 0 1024 1024" width="16" height="16">
                  <path d="M213.8 65H674.4c81.6 0 148 66.4 148 148v460.6c0 81.6-66.4 148-148 148H213.8c-81.6 0-148-66.4-148-148V213c0-81.6 66.4-148 148-148z m0 92c-30.9 0-56 25.1-56 56v460.6c0 30.9 25.1 56 56 56H674.4c30.9 0 56-25.1 56-56V213c0-30.9-25.1-56-56-56H213.8z"/>
                </svg>
                <svg v-else viewBox="0 0 1024 1024" width="16" height="16">
                  <path d="M812.2 65H351.6c-78.3 0-142.5 61.1-147.7 138.1-77 5.1-138.1 69.4-138.1 147.7v460.6c0 81.6 66.4 148 148 148h460.6c78.3 0 142.5-61.1 147.7-138.1 77-5.1 138.1-69.4 138.1-147.7V213c0-81.6-66.4-148-148-148z m-45.8 746.3c0 50.7-41.3 92-92 92H213.8c-50.7 0-92-41.3-92-92V350.7c0-50.7 41.3-92 92-92h460.6c50.7 0 92 41.3 92 92v460.6z m137.8-137.7c0 47.3-35.8 86.3-81.8 91.4V350.7c0-81.6-66.4-148-148-148H260.2c5.1-45.9 44.2-81.8 91.4-81.8h460.6c50.7 0 92 41.3 92 92v460.7z"/>
                </svg>
              </el-icon>
            </button>
            <button @click="rightSelect('quit')" class="control-btn close-btn">
              <el-icon><Close /></el-icon>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎯 主内容区域 -->
    <div class="modern-main">
      <router-view v-slot="{ Component }">
        <transition name="page-transition" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

// 当前路由路径
const currentRoute = computed(() => route.path);

// 时间显示
const currentTime = ref('');
let timeInterval: NodeJS.Timeout;

const updateTime = () => {
  const now = new Date();
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 路由导航函数
function navigateTo(path: string) {
  if (path === '/main/index/home') {
    window.api.selectView("-1");
    if (currentRoute.value.includes('/main/index')) return;
  }
  router.push(path);
}

// 窗口控制
const isMaximized = ref(false);
function rightSelect(index: string) {
  if (index === 'minimize') {
    window.api.minimize()
  } else if (index === 'maximize') {
    window.api.maximize(isMaximized.value)
    isMaximized.value = !isMaximized.value
  } else if (index === 'quit') {
    window.api.quit()
  }
}

onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }
});

</script>

<style lang="scss" scoped>
/* 🎨 现代化布局样式 */
.modern-layout {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

/* 🎯 精简化头部导航 */
.modern-header {
  height: 50px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
  position: relative;
  z-index: 1000;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .header-content {
    height: 100%;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    * {
      -webkit-app-region: no-drag;
    }
  }
}

/* 🏢 左侧导航区域 */
.left-section {
  display: flex;
  align-items: center;
  margin-left: 260px; /* 为固定侧边栏留出空间 */
}

/* 🎯 导航切换器 */
.nav-switcher {
  display: flex;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  padding: 4px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.nav-btn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  background: transparent;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  overflow: hidden;

  .nav-icon {
    font-size: 16px;
    transition: all 0.3s ease;
  }

  .nav-indicator {
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 1px;
    transition: all 0.3s ease;
  }

  &:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    transform: translateY(-1px);
  }

  &.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

    .nav-indicator {
      width: 80%;
    }
  }
}

/* 🎛️ 右侧控制区 */
.right-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #64748b;

  &.status-online {
    .status-dot {
      width: 8px;
      height: 8px;
      background: #10b981;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.time-display {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
  padding: 6px 10px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 🎮 窗口控制按钮 */
.window-controls {
  display: flex;
  gap: 6px;
}

.control-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.8);
  color: #64748b;

  &:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &.close-btn:hover {
    background: #ef4444;
    color: white;
  }
}

/* 🎯 主内容区域 */
.modern-main {
  flex: 1;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  overflow: hidden;
}

/* 🎨 页面过渡动画 */
.page-transition-enter-active,
.page-transition-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-transition-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>
