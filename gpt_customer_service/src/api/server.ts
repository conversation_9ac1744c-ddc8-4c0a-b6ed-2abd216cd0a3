import request from '../utils/request'

/**
 * 获取客服模板列表
 * @returns 
 */
export const getPromptTemplateListApi = (data) => {
  return request({
    url: '/server/get_prompt_template_list',
    method: 'post',
    data
  })
}

/**
 * 添加商店和知识库关系
 * @param data 
 * @returns 
 */
export const addShopKnowledgeApi = (data) => {
  return request({
    url: '/shop/add_shop_knowledge',
    method: 'post',
    data
  })
}

/**
 * 修改商店和知识库关系
 * @param data 
 * @returns 
 */
export const setShopKnowledgeApi = (data) => {
  return request({
    url: '/shop/edit_shop_knowledge',
    method: 'post',
    data
  })
}

/**
 * 获取模型列表
 */
export const getModelListApi = (data) => {
  return request({
    url: '/model/get_platform_model',
    method: 'post',
    data
  })
}