import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { setTextareaText, log, removeThinking, sleep } from './utils'

const api = {
  getViewId: () => VIEWID,
  setLoginStatus: (status: boolean) =>
    ipcRenderer.send('weixin:login:status', { viewId: VIEWID, status }),
  setShopName: (shopName: string) =>
    ipcRenderer.send('weixin:shop:name', { viewId: VIEWID, shopName }),
  getViewDuty: () => ipcRenderer.invoke('weixin:view:duty', VIEWID)
}
// 使用`contextBridge` api将Electron api暴露给
// 只有在启用上下文隔离时才渲染，否则
// 添加到DOM全局变量
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore（在dts中定义）
  window.electron = electronAPI
  // @ts-ignore（在dts中定义）
  window.api = api
}

var VIEWID = ''
var KNOWLEDGE = '' // 知识库

ipcRenderer.on('view:id', (_, data) => {
  VIEWID = data.viewId
  KNOWLEDGE = data.knowledge
  localStorage.setItem('viewId', VIEWID)
  localStorage.setItem('dutyStatus', data.dutyStatus)
})

interface msgAttributes {
  role: string
  content: string
  create_time: string
}

ipcRenderer.on('getGoods', async (_, data) => {
  let goodList: Array<any> = []
  if (data) {
    ipcRenderer.send('collection:state', { viewId: VIEWID, state: 1 })
    // 获取biz_magic
    const bizMagic = document.cookie.replace(
      /(?:(?:^|.*;\s*)biz_magic\s*\=\s*([^;]*).*$)|^.*$/,
      '$1'
    )
    // 获取商品列表
    const response = await fetch(
      'https://store.weixin.qq.com/shop-faas/mmchannelstradeproductcore/cgi/goods/scanProductPreview?token=&lang=zh_CN',
      {
        headers: {
          accept: 'application/json, text/plain, */*',
          'accept-language': 'zh-CN',
          biz_magic: bizMagic,
          'content-type': 'application/json',
          mcn_magic: '',
          'potter-scene': 'weixinShop',
          'sec-ch-ua': '"Not:A-Brand";v="24", "Chromium";v="134"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Windows"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-origin',
          talent_magic: '',
          wecom_magic: ''
        },
        referrer: 'https://store.weixin.qq.com/shop/goods/list',
        referrerPolicy: 'strict-origin-when-cross-origin',
        body: '{"pageSize":9999,"productStatus":[-1],"productSource":"[1,16,32]","searchSource":1,"pageNum":1,"status":[-1],"sortType":8}',
        method: 'POST',
        mode: 'cors',
        credentials: 'include'
      }
    )
    const goods = await response.json()
    // 获取产品详情
    for (let index = 0; index < goods.productList.length; index++) {
      const good = goods.productList[index]
      try {
        const goodRes = await fetch(
          `https://store.weixin.qq.com/shop-faas/mmchannelstradeproductcore/cgi/goods/getEditProductV2?token=&lang=zh_CN&productKey=%7B%22productId%22:${good.productId}%7D&needRealStock=true&preview=false&release=false`,
          {
            headers: {
              accept: 'application/json, text/plain, */*',
              'accept-language': 'zh-CN',
              biz_magic: bizMagic,
              mcn_magic: '',
              'potter-scene': 'weixinShop',
              'sec-ch-ua': '"Not:A-Brand";v="24", "Chromium";v="134"',
              'sec-ch-ua-mobile': '?0',
              'sec-ch-ua-platform': '"Windows"',
              'sec-fetch-dest': 'empty',
              'sec-fetch-mode': 'cors',
              'sec-fetch-site': 'same-origin',
              talent_magic: '',
              wecom_magic: ''
            },
            referrerPolicy: 'strict-origin-when-cross-origin',
            body: null,
            method: 'GET',
            mode: 'cors',
            credentials: 'include'
          }
        )
        const goodDetail = await goodRes.json()
        goodList.push(goodDetail.product)
      } catch (error) {}
    }
    log('获取商品列表完成')
    ipcRenderer.send('collection:state', { viewId: VIEWID, state: 2 })
    log(goodList)

    let _goods: Array<string> = []
    for (let index = 0; index < goodList.length; index++) {
      const good = goodList[index];
      let goodDesc = ''
      goodDesc += `产品ID： ${good.productKey.productId}\n`
      goodDesc += `产品标题：${good.info.title}\n`
      goodDesc += `产品分类：${good.info.recommendCategory.categoryNames.join(',')}\n`
      goodDesc += `产品主图：${JSON.stringify(good.info.headImg)}\n`
      goodDesc += `产品描述：${good.info.detail.desc}\n`
      goodDesc += `产品描述图片：${JSON.stringify(good.info.detail.detailImg)}\n`
      goodDesc += `产品选项：\n`

      for(let j = 0; j < good.productSkus.length; j++) {
        let item = good.productSkus[j]
        goodDesc += `\t规格：${item.productSkuInfo.saleParam[0].categorys[0].name}，`
        goodDesc += `规格描述：${item.productSkuInfo.saleParam[0].categorys[1].name}，`
        if (item.productSkuInfo.skuCode)
          goodDesc += `规格编码：${item.productSkuInfo.skuCode}，`
        goodDesc += `数量：${item.productSkuInfo.stockInfo.stockNum}，`
        goodDesc += `价格：${item.productSkuInfo.salePrice/100}，`
        goodDesc += `规格图片：${item.productSkuInfo.thumbImg}\n`
      }

      _goods.push(goodDesc)
    }
    log(_goods.join('\n'))
  }
})

// 监听DOM加载完成
document.addEventListener('DOMContentLoaded', () => {
  // 全局变量
  var loginState: boolean | undefined = undefined //登录状态

  var aiKfStatus: boolean | undefined = undefined //值班状态

  var shopName: string | undefined = undefined //店铺名称

  var reduceTime: any = null // 消息减少监听器

  var processedMessages = new Set<string>() // 已处理的消息ID集合，用于去重

  var processedMsgList: Array<msgAttributes> = [] // 存储处理过的消息，用于sendMessage方法引用

  var room_id: string = '' // 聊天室ID

  var messageAiStarts = false // 消息AI开始标志

  var noClickUserNum = 0 // 防止重复点击

  // 检查登录状态
  async function checkLoginStatus() {
    let _loginState: boolean | undefined = undefined

    // 微信小店登录页面的URL特征
    if (location.pathname == '/' || location.pathname == '/shop') {
      _loginState = false
    } else {
      _loginState = true
    }
    if (VIEWID != '' && loginState != _loginState) {
      log('登录状态：', _loginState)
      log('VIEWID：', VIEWID)
      loginState = _loginState
      // 获取微信小店店铺名称
      if (location.href.includes('/shop/kf')) {
        shopName = document.title
        if (shopName) {
          window.api.setShopName(shopName)
        }
      } else if (location.href.includes('/shop/home')) {
        // 跳转到客服页面
        location.href = 'https://store.weixin.qq.com/shop/kf'
      }
    }
    ipcRenderer.invoke('weixin:view:duty', VIEWID).then((dutyStatus) => {
      // log('dutyStatus', dutyStatus)
      aiKfStatus = dutyStatus
    })
    window.api.setLoginStatus(_loginState)
  }

  // 发送消息
  function sendMessage(msg: string, currentRoomId) {
    const chunkSize = 800 // 每次发送的消息长度
    const result: string[] = []
    log('用户新消息:', msg, currentRoomId)
    if (currentRoomId != room_id) return
    for (let i = 0; i < msg.length; i += chunkSize) {
      result.push(msg.slice(i, i + chunkSize))
    }
    for (let i = 0; i < result.length; i++) {
      // 适配微信小店的消息输入框和发送按钮
      const textarea = document.querySelector('textarea.text-area')
      if (textarea) {
        setTextareaText(textarea as HTMLTextAreaElement, result[i])
        // 点击发送按钮
        const enterKeyDownEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13, // 传统属性，已废弃但仍被广泛支持
          which: 13, // 传统属性，已废弃但仍被广泛支持
          bubbles: true, // 事件是否冒泡
          cancelable: true // 事件能否被取消
        })
        textarea?.dispatchEvent(enterKeyDownEvent)
        messageAiStarts = false
        noClickUserNum = 0
      }
    }
  }

  async function handleProcess(roomId: string) {
    //AI开始生成消息
    if (!aiKfStatus) return
    let msgList = processedMsgList
    if (roomId != room_id) return
    if (msgList.length && msgList[msgList.length - 1].role == 'user') {
      const res = await ipcRenderer.invoke('weixin:chat:process', {
        shopName,
        msgList,
        kbName: KNOWLEDGE,
        viewId: VIEWID
      })
      const data = JSON.parse(res)
      if (data.choices[0].message.content) {
        let text = data.choices[0].message.content
        text = removeThinking(text)
        sendMessage(text, roomId)
      } else {
        messageAiStarts = true
      }
    } else {
      messageAiStarts = true
    }
  }

  const main = async () => {
    while (true) {
      await checkLoginStatus()
      getContacts()
      await sleep(2000)
    }
  }
  main()

  function getContacts() {
    // 获取聊天室ID
    if (noClickUserNum > 0) {
      noClickUserNum--
      return
    }
    const sessionItems = document.querySelectorAll(
      '#session-list-container .session-item-container'
    )
    sessionItems.forEach((item) => {
      const hasUnread = item.querySelector('span.unread-badge') !== null
      const itemRoomId = item.getAttribute('data-room-id')
      if (hasUnread && itemRoomId != room_id && !messageAiStarts) {
        const mouseEvent = new MouseEvent('click', {
          view: window,
          bubbles: true,
          cancelable: true
        })
        item.dispatchEvent(new MouseEvent('mouseover', { bubbles: true }))
        item.dispatchEvent(new MouseEvent('mousemove', { bubbles: true }))
        setTimeout(() => {
          log(`点击后房间号:`, itemRoomId)
          item.dispatchEvent(mouseEvent)
          noClickUserNum = 3
        }, 300)
        return
      }
    })
  }
  // 拦截xhr请求获取额外信息
  const interceptXHR = (_window: Window) => {
    const originalXHR = (_window as any).XMLHttpRequest
    ;(_window as any).XMLHttpRequest = function () {
      const xhr = new originalXHR()
      const originalOpen = xhr.open
      const originalOnReadyStateChange = xhr.onreadystatechange

      xhr.open = function () {
        this._url = arguments[1]
        return originalOpen.apply(this, arguments)
      }

      xhr.onreadystatechange = function () {
        if (this.status === 200) {
          if (this._url && this.responseText) {
            // 拦截微信小店的相关API
            if (this._url.includes('/shop/kf/cgi/shop/getBaseInfo')) {
              try {
                const data = JSON.parse(this.responseText)?.shopInfo
                if (data && data.name) {
                  window.api.setShopName(data.name)
                }
              } catch (e) {
                log('解析店铺信息失败', e)
              }
            }

            // 拦截微信小店的消息API
            if (this._url.includes('/shop/commkf/msg')) {
              try {
                if (this._url.includes('action=get_room_msg')) {
                  // 消息列表
                  if (JSON.parse(this.responseText)) {
                    const data = JSON.parse(this.responseText)
                    let list: any = data.list
                    if (list && list.length > 0) {
                      getChatMessages(list)
                    }
                  }
                }
                if (this._url.includes('action=get_sync_msg')) {
                  // 新消息
                  const data = JSON.parse(this.responseText)
                  let list: any = data.list
                  if (list && list.length > 0) {
                    getChatMessages(list)
                  }
                }
              } catch (e) {
                log('解析消息失败', e)
              }
            }
            if (this._url.includes('/shop/commkf/room')) {
              // https://store.weixin.qq.com/shop/commkf/summary?action=get_session_summary //全部会话
              // https://store.weixin.qq.com/shop/commkf/summary?action=get_open_session_summary //当前会话
              if (this._url.includes('action=get_room_info')) {
                // 点击对应会话
                try {
                  room_id = JSON.parse(this.responseText).room_session.room_id
                  processedMsgList = []
                  processedMessages = new Set()
                  log('换人了')
                } catch (e) {}
              }
              if (this._url.includes('action=get_room_recommend_info')) {
                try {
                  const data = JSON.parse(this.responseText)
                  let products: any = data.recommend_product_infos
                  if (products && products.length > 0) {
                    reportProductInfo(products)
                  }
                } catch (e) {
                  log('解析消息失败', e)
                }
              }
            }
          }
        }
        if (originalOnReadyStateChange) {
          originalOnReadyStateChange.apply(this, arguments)
        }
      }
      return xhr
    }
  }

  function getChatMessages(list: any) {
    // 消息处理
    let msgList: any = []
    let hasNewMessages: boolean = false
    let msgTypes = [1, 11] //允许进入的消息类型
    if (room_id && room_id != list[0].room_id) {
      log('用户与消息房间不匹配')
      return
    }
    room_id = list[0].room_id
    list.forEach((item: any) => {
      if (msgTypes.indexOf(item.msg_type) != -1) {
        // 创建唯一ID
        const msgId = `${item.msg_type}_${item.msg_direction}_${item.create_time}`
        // 检查消息是否已处理过
        if (!processedMessages.has(msgId)) {
          // 未处理过的消息，标记为有新消息
          // 添加到已处理集合

          if (item.msg_type == 1) {
            if (item.msg_direction == 1 || item.msg_direction == 2) {
              hasNewMessages = true
              processedMessages.add(msgId)
              let msg: object = {
                role: item.msg_direction == 2 ? 'assistant' : 'user',
                content: JSON.parse(item.msg_kf_content).content,
                create_time: item.create_time
              }
              msgList.push(msg)
            }
          } else if (item.msg_type == 11) {
            // 商品信息
            if (item.msg_direction == 1 || item.msg_direction == 2) {
              let cardInfo = JSON.parse(JSON.parse(item.msg_kf_content).content)
              hasNewMessages = true
              processedMessages.add(msgId)
              let proName = `以下是可供参考的商品信息，请根据用户问题精准回答：\n商品名称：${cardInfo.product_title}，商品价格：${cardInfo.selling_price / 100}元`
              let msg: object = {
                // role: item.msg_direction == 2 ? 'assistant' : 'user',
                role: 'system',
                content: proName,
                create_time: item.create_time
              }
              msgList.push(msg)
            }
          } else if (item.msg_type == 12) {
            // 订单信息
            if (item.msg_direction == 1 || item.msg_direction == 2) {
              hasNewMessages = true
              processedMessages.add(msgId)

              let order = JSON.parse(item.msg_kf_content)
              let msg: object = {
                role: item.msg_direction == 2 ? 'assistant' : 'user',
                content: JSON.parse(order.content),
                create_time: item.create_time
              }
              msgList.push(msg)
            }
          }
        }
      }
    })
    // 更新全局变量，存储处理过的消息，以便sendMessage方法引用
    if (msgList.length > 0) {
      processedMsgList = [...processedMsgList, ...msgList]
      processedMsgList.sort((a: any, b: any) => a.create_time * 1 - b.create_time * 1)
    }

    if (hasNewMessages) {
      // 检查是否所有新消息都是assistant角色
      const hasUserMessages = msgList.some((msg: any) => msg.role === 'user')
      // 仅当有用户消息时才执行后续操作
      if (hasUserMessages) {
        clearTimeout(reduceTime)
        reduceTime = setTimeout(() => {
          // 获取用户最新消息
          log('新消息:', room_id, processedMsgList.length, processedMessages)
          if (processedMsgList.length > 0) {
            const latestUserMessage = processedMsgList[processedMsgList.length - 1]
            if (latestUserMessage.role == 'user') {
              var currentRoomId = JSON.parse(JSON.stringify(room_id))
              handleProcess(currentRoomId)
            }
          }
        }, 500)
      } else {
        log('当前为客服发送的消息')
      }
    }

    return msgList
  }

  // 处理商品信息上报
  function reportProductInfo(list: any = []) {
    if (list.length > 0) {
      const products = list.map((item) => {
        let head_imgs: string = ''
        if (item.head_imgs.length > 0) {
          head_imgs = item.head_imgs[0]
        }
        return {
          name: item?.product_name || '',
          price: item?.min_product_price || '',
          image: head_imgs,
          id: item?.product_id || ''
        }
      })
      log('商品信息：', products)
      if (products.length > 0) {
        ipcRenderer.send('weixin:product:info', { viewId: VIEWID, products })
      }
    }
  }
  // 初始化拦截器
  try {
    interceptXHR(window)
  } catch (e) {
    log('初始化拦截器失败', e)
  }
})
