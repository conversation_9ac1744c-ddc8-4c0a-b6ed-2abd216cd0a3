import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { setTextareaText, log, removeThinking, sleep } from './utils'

declare global {
  interface Window {
    api: typeof api
  }
}

const api = {
  setLoginStatus: (status: boolean) =>
    ipcRenderer.send('douyin:login:status', { viewId: VIEWID, status }),
  setShopName: (shopName: string) =>
    ipcRenderer.send('douyin:shop:name', { viewId: VIEWID, shopName })
}

// 使用`contextBridge` api将Electron api暴露给
// 只有在启用上下文隔离时才渲染，否则
// 添加到DOM全局变量
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore（在dts中定义）
  window.electron = electronAPI
  // @ts-ignore（在dts中定义）
  window.api = api
}

let VIEWID = localStorage.getItem('viewId') || ''
let DUTY_STATUS = false // 负责状态
let KNOWLEDGE = '' // 知识库
let isGetGoods = false // 是否获取商品列表
;(async () => {
  isGetGoods = (await ipcRenderer.invoke('get-conf', `${VIEWID}.isGetGoods`)) || false
  log('lisgetgoods', isGetGoods)
})()
let getGoodsState = 0 // 获取商品列表状态, 0: 未开始, 1: 正在获取, 2: 获取完成

ipcRenderer.on('view:id', (_, data) => {
  VIEWID = data.viewId
  KNOWLEDGE = data.knowledge
  DUTY_STATUS = data.dutyStatus
  localStorage.setItem('viewId', VIEWID)
  localStorage.setItem('dutyStatus', data.dutyStatus)
})

ipcRenderer.on('main:process', (_, data) => {
  DUTY_STATUS = data
})

ipcRenderer.on('getGoods', (_, data) => {
  isGetGoods = data
  ipcRenderer.send('set-conf', `${VIEWID}.isGetGoods`, isGetGoods)
  getGoodsState = 0
  location.href = 'https://fxg.jinritemai.com/ffa/g/list'
})

interface msgAttributes {
  role: string
  content: string
}

let taskId = ''

function downloadExcel(url) {
  getGoodsState = 2
  ipcRenderer.send('collection:state', { viewId: VIEWID, state: getGoodsState })
  ipcRenderer.send('set-conf', `${VIEWID}.isGetGoods`, false)
  log(url)
  location.reload()
}

async function cheackDownloadProduct() {
  if (taskId == '') return
  const response = await fetch(
    'https://fxg.jinritemai.com/product/tproduct/getDownloadProductInfo?page=0&pageSize=10&appid=1',
    {
      headers: {
        accept: 'application/json, text/plain, */*',
        'accept-language': 'zh-CN',
        priority: 'u=1, i',
        'sec-ch-ua': '"Not:A-Brand";v="24", "Chromium";v="134"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'x-tt-from-appid': 'ffa-goods',
        'x-tt-from-end': 'PC',
        'x-tt-from-page': 'https://fxg.jinritemai.com/ffa/g/excel',
        'x-tt-from-version': '1.0.0.7957'
      },
      referrer: 'https://fxg.jinritemai.com/ffa/g/excel',
      referrerPolicy: 'strict-origin-when-cross-origin',
      body: null,
      method: 'GET',
      mode: 'cors',
      credentials: 'include'
    }
  )
  const data = await response.json()
  for (let index = 0; index < data.data.length; index++) {
    const task = data.data[index]
    if (task.task_id == taskId) {
      if (task.status == 3) {
        await downloadExcel(task.url)
        return
      }
    }
  }
  setTimeout(() => {
    cheackDownloadProduct()
  }, 2000)
}

async function asyncDownloadProduct() {
  getGoodsState = 1
  ipcRenderer.send('collection:state', { viewId: VIEWID, state: getGoodsState })
  const response = await fetch(
    'https://fxg.jinritemai.com/product/tproduct/asyncDownloadProduct?page=0&pageSize=20&check_status=&presell_type=&group_id=&sku_type=&FORCE_FETCH=0.6437280577760491&tab=all&business_type=4&is_online=1&not_for_sale_search_type=1&from_mng=1&supply_status=&appid=1',
    {
      headers: {
        accept: 'application/json, text/plain, */*',
        'accept-language': 'zh-CN',
        priority: 'u=1, i',
        'sec-ch-ua': '"Not:A-Brand";v="24", "Chromium";v="134"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'x-tt-from-appid': 'ffa-goods',
        'x-tt-from-end': 'PC',
        'x-tt-from-page': 'https://fxg.jinritemai.com/ffa/g/list',
        'x-tt-from-version': '1.0.0.7957'
      },
      referrerPolicy: 'strict-origin-when-cross-origin',
      body: null,
      method: 'GET',
      mode: 'cors',
      credentials: 'include'
    }
  )
  const data = await response.json()
  taskId = data.data.task_id
  cheackDownloadProduct()
}

// 监听DOM加载完成
document.addEventListener('DOMContentLoaded', () => {
  log('DOM加载完成')
  // 全局变量
  let loginState: boolean | undefined = undefined
  let shopName: string | undefined = undefined
  const msgManager = new Map<string, Array<msgAttributes>>() // 聊天记录

  function toUrl(url) {
    if (location.href.includes(url)) return
    location.href = url
  }

  // 检查登录状态
  function checkLoginStatus(): void {
    if (location.href.includes('/login/common')) {
      window.api.setLoginStatus(false)
    } else {
      log('VIEWID：', VIEWID)
      window.api.setLoginStatus(true)
      if (window['__SSR_CONFIG_ECOM_FXG_ADMIN']) {
        shopName =
          window['__SSR_CONFIG_ECOM_FXG_ADMIN'].initialData['fxg-admin'].userData.user.shop_name
        if (shopName) {
          window.api.setShopName(shopName)
        }
        log('isGetGoods', isGetGoods)
        if (isGetGoods) {
          toUrl('https://fxg.jinritemai.com/ffa/g/list')
          asyncDownloadProduct()
        } else {
          toUrl('https://im.jinritemai.com/pc_seller')
        }
      }
    }
  }

  checkLoginStatus()

  // 获取用户订单
  // function getUserOrder(): Promise<any> {
  //   interface orderObj {
  //     orderNumber?: string
  //     status?: string
  //     status1?: string
  //     status2?: string
  //     paymentTime?: string
  //   }
  //   const orderList: Array<orderObj> = []
  //   return new Promise((resolve) => {
  //     waitForElement('#leftSide > div > div > div > div > div > div.ecom-collapse').then(() => {
  //       document.querySelector('#leftSide > div')?.scrollTo({ top: 1000000 })
  //       setTimeout(() => {
  //         const orderElements = document.querySelectorAll(
  //           '#leftSide > div > div > div > div > div > div.ecom-collapse'
  //         )
  //         orderElements.forEach((element) => {
  //           const orderObj: orderObj = {}
  //           // 获取订单号
  //           const orderNumberEle = element.querySelector(
  //             'div.ecom-collapse-item > div.ecom-collapse-header > div > div> div > div[data-btm] > div > span'
  //           )
  //           if (orderNumberEle) {
  //             orderObj['orderNumber'] = orderNumberEle.textContent || ''
  //           }
  //           // 获取订单状态
  //           const orderStatusEle = element.querySelector(
  //             'div.ecom-collapse-item > div.ecom-collapse-header > div > div:nth-child(1) > div.ecom-sp-tag > div.sp-tag-content'
  //           )
  //           if (orderStatusEle) {
  //             orderObj['status'] = orderStatusEle.textContent || ''
  //           } else {
  //             const orderStatusEle = element.querySelectorAll(
  //               'div.ecom-collapse-item > div.ecom-collapse-header > div > div:nth-child(1) > div.ecom-dorami-tag-group-wrapper > div.ecom-sp-tag > div.sp-tag-content'
  //             )
  //             if (orderStatusEle.length > 0) {
  //               orderObj['status'] = orderStatusEle[0].textContent || ''
  //               orderObj['status1'] = orderStatusEle[1].textContent || ''
  //             }
  //           }
  //           // 获取订单状态1
  //           const orderStatusEle1 = element.querySelector(
  //             'div.ecom-collapse-item > div.ecom-collapse-header > div > div > div > div > div.rc-overflow > div.rc-overflow-item > div.ecom-sp-tag > div.sp-tag-content'
  //           )
  //           if (orderStatusEle1) {
  //             orderObj['status2'] = orderStatusEle1.textContent || ''
  //           }
  //           // 获取订单付款时间
  //           const orderTimeEle = element.querySelector(
  //             'div.ecom-collapse-item > div.ecom-collapse-header > div > div:nth-child(2) > div > div > div > div > span > div > div > span'
  //           )
  //           if (orderTimeEle) {
  //             orderObj['paymentTime'] = orderTimeEle.textContent || ''
  //           }
  //           const orderContent = element.querySelector(
  //             'div.ecom-collapse-item > div.ecom-collapse-content'
  //           )
  //           if (orderContent) {
  //             const orderProductName = orderContent.querySelector('div.textSpecial > div > span')
  //             if (orderProductName) {
  //               orderObj['productName'] = orderProductName.textContent || ''
  //             }
  //             log(
  //               orderContent.querySelectorAll(
  //                 'div > div > div:nth-child(1) > div:nth-child(3) > div:nth-child(1) > div > div'
  //               )
  //             )
  //           }

  //           orderList.push(orderObj)
  //         })
  //         log('获取到的订单列表：', orderList)
  //         resolve(orderList)
  //       }, 2000)
  //     })
  //   })
  // }

  // 滚动聊天记录
  function scrollChat(): Promise<void> {
    // 记录滚动次数
    let scrollCount = 0
    return new Promise((resolve) => {
      function scroll(): void {
        scrollCount++
        const topText = document.querySelector(
          '#workspace-chat > div > div > div.messageList > div:nth-child(2) > div:nth-child(1) > div'
        )
        document
          .querySelector('#workspace-chat > div > div > div.messageList')
          ?.scrollTo({ top: 0 })
        if (topText && topText.textContent && topText.textContent.includes('已经到顶啦')) {
          ;(
            document.querySelector('#workspace-chat > div > div > div.messageList')
              ?.nextSibling as HTMLElement
          )?.click()
          resolve(void 0)
        } else if (scrollCount >= 3) {
          ;(
            document.querySelector('#workspace-chat > div > div > div.messageList')
              ?.nextSibling as HTMLElement
          )?.click()
          resolve(void 0)
        } else {
          setTimeout(() => {
            scroll()
          }, 2000)
        }
      }
      scroll()
    })
  }

  // 获取聊天记录
  async function getChatMessages(): Promise<msgAttributes[]> {
    log('获取聊天记录')
    await scrollChat()
    const msgList: Array<msgAttributes> = []
    if (location.href.includes('/main/workspace')) {
      let msgItem = document.querySelectorAll(
        '#workspace-chat > div > div > div.messageList > div > div.msgItemWrap'
      )
      if (msgItem.length == 0) {
        msgItem = document.querySelectorAll(
          '#workspace-chat > div > div > div.messageList > div > div div.list_items>div'
        )
      }
      for (let i = 0; i < msgItem.length; i++) {
        const meMsg = msgItem[i].querySelector('.leaveMessage.messageIsMe > pre > span')
        const notMeMsg = msgItem[i].querySelector('.leaveMessage.messageNotMe > pre > span')
        const chatdCard = msgItem[i].querySelector('.chatd-card-main')
        const image = msgItem[i].querySelector(
          'div > div > div:nth-child(2) > div > img.auxo-dropdown-trigger'
        )
        if (meMsg) {
          const msgText = meMsg.textContent || (meMsg as HTMLElement).innerText
          msgList.push({
            role: 'assistant',
            content: msgText
          })
        } else if (notMeMsg) {
          const msgText = notMeMsg.textContent || (notMeMsg as HTMLElement).innerText
          msgList.push({
            role: 'user',
            content: msgText
          })
        } else if (chatdCard) {
          const cardInfo = {
            name: chatdCard.querySelector(
              'div > div > div > div > div.auxo-dropdown-trigger > div > div.chatd-card-main > div:nth-child(1) > div > div > div:nth-child(1) > div > div > span'
            )?.textContent,
            price: chatdCard.querySelector('.chatd-price-price')?.textContent?.replaceAll('\n', '')
          }
          if (cardInfo.name && cardInfo.price) {
            const _shopName = msgItem[i].querySelector(
              'div > div > div:nth-child(2) > div > div[style="margin-bottom: 6px;"]'
            )
            if (_shopName && _shopName.textContent == '客服助手') {
              msgList.push({
                role: 'system',
                content: `以下是可供参考的商品信息，请根据用户问题精准回答：\n商品名称：${cardInfo.name}，\n商品价格：${cardInfo.price}`
              })
            } else if (_shopName && _shopName.textContent == shopName) {
              // 客服发给客户的产品卡片
              msgList.push({
                role: 'system',
                content: `以下是可供参考的商品信息，请根据用户问题精准回答：\n商品名称：${cardInfo.name}，\n商品价格：${cardInfo.price}`
              })
            } else {
              // 客户发给客服的产品卡片
              msgList.push({
                role: 'system',
                content: `以下是可供参考的商品信息，请根据用户问题精准回答：\n商品名称：${cardInfo.name}，\n商品价格：${cardInfo.price}`
              })
            }
          }
        } else if (image) {
          const _shopName = msgItem[i].querySelector(
            'div > div > div:nth-child(2) > div > div[style="margin-bottom: 6px;"]'
          )
          const imgSrc = (image as HTMLImageElement).src
          if (_shopName && _shopName.textContent == shopName) {
            // 客服发给客户的图片
            msgList.push({
              role: 'assistant',
              content: `这是一个图片，${imgSrc}`
            })
          } else {
            // 客户发给客服的图片
            msgList.push({
              role: 'user',
              content: `这是一个图片，${imgSrc}`
            })
          }
        }
      }
    }
    log(msgList)
    return msgList
  }

  // 发送消息
  function sendMessage(msg: string): void {
    log('发送消息')
    const chunkSize = 800 // 每次发送的消息长度
    const result: string[] = []
    for (let i = 0; i < msg.length; i += chunkSize) {
      result.push(msg.slice(i, i + chunkSize))
    }
    for (let i = 0; i < result.length; i++) {
      const textarea = document.querySelector('textarea[data-qa-id="qa-send-message-textarea"]')
      if (textarea) {
        setTextareaText(textarea as HTMLTextAreaElement, result[i])
        ;(document.querySelector('div[data-qa-id="qa-send-message-button"]') as HTMLElement).click()
      }
    }
  }

  // 单次流程处理
  async function handleProcess(userName: string): Promise<void> {
    const msgList = await getChatMessages()
    if (msgList.length && msgList[msgList.length - 1].role == 'user') {
      if (msgList != msgManager.get(userName)) {
        msgManager.set(userName, msgList)
        const res = await ipcRenderer.invoke('douyin:chat:process', {
          shopName,
          msgList,
          kbName: KNOWLEDGE,
          viewId: VIEWID
        })
        const data = JSON.parse(res)
        log(data)
        if (data.choices[0].message.content) {
          let text = data.choices[0].message.content
          text = removeThinking(text)
          sendMessage(text)
        }
      }
    }
  }

  window['chat'] = async function() {
    const res = await ipcRenderer.invoke('douyin:chat:process', {
      shopName: "",
      msgList: [{
        role: 'user',
        content: '你好'
      }],
      kbName: KNOWLEDGE,
      viewId: VIEWID
    })
    const data = JSON.parse(res)
    log(data);
  }

  // 新消息处理
  async function newMsgProcess(session): Promise<void> {
    const sessionName = session.querySelector('div[title]')?.getAttribute('title') || ''
    await handleProcess(sessionName)
  }

  // 检查是否有新消息
  const checkNewMessage = (): HTMLElement | null => {
    let newMessageChat: HTMLElement | null = null
    const chatList = Array.from(
      document.querySelectorAll("[data-qa-id*='qa-conversation-chat-item']")
    )
    for (let i = 0; i < chatList.length; i++) {
      const chatItem = chatList[i] as HTMLElement
      const timeEl = chatItem?.children[3] as HTMLElement | null
      if (
        timeEl?.innerText?.includes('秒') ||
        timeEl?.innerText?.includes('分钟') ||
        timeEl?.innerText?.includes('小时') ||
        timeEl?.innerText?.includes('天')
      ) {
        newMessageChat = chatItem
        break
      }
    }

    return newMessageChat
  }

  // 检查新消息 然后 AI回复
  const checkMessageAndReply = async (): Promise<void> => {
    try {
      if (loginState) {
        // 发送消息到主进程
        const newMessageChat = checkNewMessage()
        if (newMessageChat) {
          newMessageChat.click()
          await sleep(1500)
          await newMsgProcess(newMessageChat)
        }
      }
    } catch (e) {
      console.log(e)
    }
  }

  const main = async (): Promise<void> => {
    while (true) {
      if (DUTY_STATUS) {
        log('开始检查新消息')
        await checkMessageAndReply() // 必须要await  要不然 click 有sleep  会导致回复两条
        await sleep(1000)
      } else {
        await sleep(2000)
      }
    }
  }
  main()
})
