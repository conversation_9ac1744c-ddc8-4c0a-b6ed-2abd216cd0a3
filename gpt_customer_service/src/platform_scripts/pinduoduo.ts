import { contextBridge, ipcRenderer } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { log, sleep, removeThinking, setTextareaText } from './utils'
import { console } from 'inspector'

const api = {}

// 使用`contextBridge` api将Electron api暴露给
// 只有在启用上下文隔离时才渲染，否则
// 添加到DOM全局变量
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore（在dts中定义）
  window.electron = electronAPI
  // @ts-ignore（在dts中定义）
  window.api = api
}

interface msgAttributes {
  role: string
  content: string
}

let VIEWID = '' // 视图ID
let DUTY_STATUS = false // 负责状态
let KNOWLEDGE = '' // 知识库
let loginState: boolean | undefined = undefined // 登录状态
let shopName = '' // 店铺名称
const msgManager = new Map<string, Array<msgAttributes>>() // 聊天记录
let isGetGoods = localStorage.getItem('isGetGoods') === 'true' // 是否获取商品列表
let getGoodsState = 0 // 获取商品列表状态, 0: 未开始, 1: 正在获取, 2: 获取完成

ipcRenderer.on('view:id', (_, data) => {
  VIEWID = data.viewId
  KNOWLEDGE = data.knowledge
  DUTY_STATUS = data.dutyStatus
  // USER_NAME = data.userName
  // PASSWORD = data.password
  localStorage.setItem('viewId', VIEWID)
  localStorage.setItem('dutyStatus', data.dutyStatus)
})

ipcRenderer.on('main:process', (_, data) => {
  DUTY_STATUS = data
})

ipcRenderer.on('getGoods', (_, data) => {
  isGetGoods = data
  localStorage.setItem('isGetGoods', isGetGoods.toString())
  getGoodsState = 0
})

const originalFetch = window.fetch
let antiContent = ''
window.fetch = async function (...args) {
  if (args[0] === '/glide/v2/mms/addProperties/goodsList' && antiContent == '' && isGetGoods) {
    if (args[1]) {
      const header = args[1]?.headers
      if (header) {
        antiContent = header['Anti-Content']
        getGoodList()
      }
    }
  }
  return originalFetch(...args)
}

let goodList: Array<any> = []

// 产品采集
async function getGoodList(page = 1) {
  getGoodsState = 1
  ipcRenderer.send('collection:state', { viewId: VIEWID, state: getGoodsState })
  const goodsListRes = await fetch(
    'https://mms.pinduoduo.com/vodka/v2/mms/query/display/mall/goodsList',
    {
      headers: {
        'content-type': 'application/json',
        'anti-content': antiContent
      },
      referrer: 'https://mms.pinduoduo.com/goods/goods_list',
      referrerPolicy: 'strict-origin-when-cross-origin',
      body: `{"pre_sale_type":4,"page":${page},"shipment_time_type":3,"is_onsale":1,"sold_out":0,"size":100}`,
      method: 'POST',
      mode: 'cors',
      credentials: 'include'
    }
  )
  const goodsList = await goodsListRes.json()
  if (goodsList.success && goodsList.errorCode === 1000000) {
    const goodsListData = goodsList.result.goods_list
    for (let index = 0; index < goodsListData.length; index++) {
      const good = goodsListData[index]
      const goodId = good.id
      const detailRes = await fetch(
        'https://mms.pinduoduo.com/glide/v2/mms/query/commit/on_shop/detail',
        {
          headers: {
            'content-type': 'application/json',
            'anti-content': antiContent
          },
          referrerPolicy: 'strict-origin-when-cross-origin',
          body: `{\"goods_id\":${goodId}}`,
          method: 'POST',
          mode: 'cors',
          credentials: 'include'
        }
      )
      const detailData = await detailRes.json()
      const detail = detailData.result
      // goodList[detail.goods_id] = detail
      // 获取详情页
      const previewRes = await fetch(
        'https://mms.pinduoduo.com/gorse/mms/goods/decoration/preview',
        {
          headers: {
            'anti-content': antiContent,
            'content-type': 'application/json'
          },
          body: `{\"goods_commit_id\":${detail.goods_commit_id},\"goods_id\":${detail.goods_id}}`,
          method: 'POST',
          mode: 'cors',
          credentials: 'include'
        }
      )
      const previewData = await previewRes.json()
      // 详情页链接
      log('详情页链接', previewData.result.preview_url)
      const _good = {
        ...detail,
        preview_url: previewData.result.preview_url
      }
      goodList.push(_good)
    }
    log(goodList)
    if (goodsList.result.total !== goodList.length) {
      page++
      await sleep(5000)
      getGoodList(page)
    } else {
      getGoodsState = 2
      ipcRenderer.send('collection:state', { viewId: VIEWID, state: getGoodsState })
      parseGoods()
    }
  }
}

// 解析商品
function parseGoods() {
  let goods: Array<string> = []
  for (let index = 0; index < goodList.length; index++) {
    const good = goodList[index];
    let goodDesc = ''
    goodDesc += `产品ID： ${good.goods_id}\n`
    goodDesc += `产品标题：${good.goods_name}\n`
    goodDesc += `产品分类：${good.cats.join('>')}\n`
    goodDesc += `产品主图：${JSON.stringify(good.carousel_gallery.map((item)=>item.url))}\n`
    goodDesc += `产品描述：${good.goods_desc}\n`
    goodDesc += `产品分享描述：${good.share_desc}\n`
    goodDesc += `产品详情链接：${good.preview_url}\n`
    goodDesc += `产品选项：\n`

    for(let j = 0; j < good.skus.length; j++) {
      let item = good.skus[j]
      goodDesc += `\t规格：${item.spec[0].parent_name}，`
      goodDesc += `规格描述：${item.spec[0].spec_name}，`
      if (item.out_sku_sn)
        goodDesc += `规格编码：${item.out_sku_sn}，`
      goodDesc += `数量：${item.quantity}，`
      goodDesc += `单买价格：${item.price/100}，`
      goodDesc += `拼单价格：${item.multi_price/100}，`
      goodDesc += `参考价格：${item.market_price/100}，`
      goodDesc += `规格图片：${item.thumb_url}\n`
    }
    goods.push(goodDesc)
  }
  log(goods.join('\n'))
}
// 产品采集-end

// 监听DOM加载完成
document.addEventListener('DOMContentLoaded', () => {
  // async function setUserNameInput(userNameInput: HTMLInputElement, userName: string) {
  //   userNameInput.dispatchEvent(new Event('focus', { bubbles: true, cancelable: true }))
  //   let lsKey = ''
  //   for (let i = 0; i < userName.length; i++) {
  //     let key = userName[i]
  //     lsKey += key
  //     // 1. 模拟键盘按下事件
  //     const keyDownEvent = new KeyboardEvent('keydown', {
  //       bubbles: true,
  //       cancelable: true,
  //       key: key
  //     })
  //     userNameInput.dispatchEvent(keyDownEvent)
  //     userNameInput.value = lsKey
  //     // 2. 触发input事件
  //     const inputEvent = new InputEvent('input', {
  //       bubbles: true,
  //       cancelable: true,
  //       data: lsKey
  //     })
  //     userNameInput.dispatchEvent(inputEvent)

  //     // 3. 模拟键盘抬起事件
  //     const keyUpEvent = new KeyboardEvent('keyup', {
  //       bubbles: true,
  //       cancelable: true,
  //       key: key
  //     })
  //     userNameInput.dispatchEvent(keyUpEvent)

  //     // 4. 模拟change事件
  //     const changeEvent = new Event('change', {
  //       bubbles: true,
  //       cancelable: true
  //     })
  //     userNameInput.dispatchEvent(changeEvent)
  //   }
  //   await sleep(500)
  //   userNameInput.dispatchEvent(new Event('blur', { bubbles: true, cancelable: true }))
  // }

  // 检查登录状态
  async function checkLoginStatus(): Promise<void> {
    // await login()
    let _loginState: boolean | undefined = undefined
    if (location.href.includes('/login/')) {
      _loginState = false
    } else {
      _loginState = true
    }
    if (VIEWID != '' && loginState != _loginState) {
      log('登录状态：', _loginState)
      log('VIEWID：', VIEWID)
      loginState = _loginState
      if (window['__NEXT_DATA__']) {
        shopName =
          window['__NEXT_DATA__']['props']['headerProps']['serverData']['userInfo']['mall'][
            'mall_name'
          ]
        if (shopName && VIEWID) {
          ipcRenderer.send('pinduoduo:shop:name', { viewId: VIEWID, shopName })
        }
        if (!isGetGoods) {
          location.href = 'https://mms.pinduoduo.com/chat-merchant/index.html'
        }
      }
      ipcRenderer.send('pinduoduo:login:status', { viewId: VIEWID, status: _loginState })
    } else {
      if (loginState) {
        if (getGoodsState == 2) {
          log('获取商品列表完成')
          getGoodsState = 0
          isGetGoods = false
          localStorage.setItem('isGetGoods', isGetGoods.toString())
          location.reload()
        }
        if (isGetGoods && getGoodsState == 0 && !location.href.includes('/goods/goods_list')) {
          getGoodsState = 1
          location.href = 'https://mms.pinduoduo.com/goods/goods_list?msfrom=mms_sidenav'
        }
      }
    }
  }

  function checkNewMessage(): HTMLElement | null {
    const chatItems = document.querySelectorAll('*[class^="chat-item"]')
    let newMessageChat: HTMLElement | null = null
    for (let i = 0; i < chatItems.length; i++) {
      const chatItem = chatItems[i] as HTMLElement
      const unreplyTime = chatItem.querySelector('*[class^="chat-time"]') as HTMLElement
      if (
        unreplyTime?.innerText?.includes('秒后超时') ||
        unreplyTime?.innerText?.includes('已等待')
      ) {
        newMessageChat = chatItem
        break
      }
    }
    return newMessageChat
  }

  function getMessageList() {
    let messageList: Array<msgAttributes> = []
    const chatItems = document.querySelectorAll('.msg-list li')
    for (let i = 0; i < chatItems.length; i++) {
      const chatItem = chatItems[i] as HTMLElement
      let role = 'assistant'
      let content = ''

      if (chatItem.querySelector('.buyer-item')) {
        role = 'user'
      } else if (chatItem.querySelector('.cs-item')) {
        role = 'assistant'
      }
      if (chatItem.querySelector('.image-msg')) {
        const imgSrc = chatItem.querySelector('.img-msg-thumb')?.getAttribute('src')
        content = `这是一张图片 ${imgSrc}`
      } else if (chatItem.querySelector('.good-card,.goods-card')) {
        const goodCard = chatItem.querySelector('.good-card,.goods-card')
        const goodId = ((goodCard?.querySelector('.good-id') as HTMLElement)?.innerText || '')
          .replace('复制', '')
          .replace('商品ID：', '')
        const goodImg = goodCard?.querySelector('.good-detail>img')?.getAttribute('src')
        const goodName = (goodCard?.querySelector('.good-name') as HTMLElement)?.innerText
        const goodPrice = (goodCard?.querySelector('.good-price') as HTMLElement)?.innerText
        role = 'system'
        content = `以下是可供参考的商品信息，请根据用户问题精准回答：\n产品ID:${goodId}, 产品名称:${goodName} 产品图片:${goodImg}, 产品价格:${goodPrice}`
      } else {
        const contentBox = chatItem.querySelector('.msg-content-box') as HTMLElement | null
        content = contentBox?.innerText ?? ''
      }
      if (content) {
        messageList.push({
          role: role,
          content: content
        })
      }
    }
    return messageList
  }

  function sendMessage(text: string) {
    log('发送消息')
    const textarea = document.querySelector('textarea#replyTextarea')
    if (textarea) {
      setTextareaText(textarea as HTMLTextAreaElement, text)

      // 创建并触发回车按下事件
      const enterEvent = new KeyboardEvent('keydown', {
        key: 'Enter',
        code: 'Enter',
        keyCode: 13, // 回车键的keyCode
        which: 13,
        bubbles: true,
        cancelable: true
      })

      // 派发事件
      textarea.dispatchEvent(enterEvent)
    }
  }

  async function newMsgProcess(newMessageChat: HTMLElement): Promise<void> {
    log('新消息')
    const messageList = await getMessageList()
    const userName =
      newMessageChat.querySelector('*[data-random]')?.getAttribute('data-random') || ''
    if (messageList.length && messageList[messageList.length - 1].role == 'user') {
      if (messageList != msgManager.get(userName)) {
        msgManager.set(userName, messageList)
        const res = await ipcRenderer.invoke('pinduoduo:chat:process', {
          shopName,
          msgList: messageList,
          kbName: KNOWLEDGE,
          viewId: VIEWID
        })
        const data = JSON.parse(res)
        log(data)
        if (data.choices[0].message.content) {
          let text = data.choices[0].message.content
          text = removeThinking(text)
          sendMessage(text)
        }
      }
    }
  }

  window['chat'] = async () => {
    const res = await ipcRenderer.invoke('pinduoduo:chat:process', {
      shopName,
      msgList: [],
      kbName: KNOWLEDGE,
      viewId: VIEWID
    })
    return res
  }

  const main = async (): Promise<void> => {
    let newMsgSatus = true
    while (true) {
      ;(document.querySelector('#app > div > div > div > div.close-icon') as HTMLElement)?.click()
      await checkLoginStatus()
      if (DUTY_STATUS) {
        // 检查新消息
        if (newMsgSatus) {
          // log('开始检查新消息')
          const newMessageChat = checkNewMessage()
          if (newMessageChat) {
            newMsgSatus = false
            const _div = newMessageChat.querySelector('&>div')
            _div?.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }))
            _div?.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }))
            await sleep(1500)
            await newMsgProcess(newMessageChat)
            newMsgSatus = true
          }
        }
        await sleep(1500)
      } else {
        await sleep(2000)
      }
    }
  }
  main()
})
