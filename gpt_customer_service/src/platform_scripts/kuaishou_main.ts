import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { log as __log, removeThinking, sleep } from './utils'
// Extend the Window interface to include the 'api' property

const api = {
  getViewId: () => VIEWID,
  setLoginStatus: (status: boolean) =>
    ipcRenderer.send('kuaishou:login:status', { viewId: VIEWID, status }),
  setShopName: (shopName: string) =>
    ipcRenderer.send('kuaishou:shop:name', { viewId: VIEWID, shopName })
}

// 使用`contextBridge` api将Electron api暴露给渲染进程
// 只有在启用上下文隔离时才渲染，否则添加到DOM全局变量
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore（在dts中定义）
  window.electron = electronAPI
  // @ts-ignore（在dts中定义）
  window.api = api
}

var VIEWID = localStorage.getItem('viewId') || ''
var DUTY_STATUS = false // 负责状态
var KNOWLEDGE = '' // 知识库

ipcRenderer.on('view:id', (_, data) => {
  VIEWID = data.viewId
  KNOWLEDGE = data.knowledge
  DUTY_STATUS = data.dutyStatus
  localStorage.setItem('viewId', VIEWID)
  localStorage.setItem('dutyStatus', data.dutyStatus)
})

ipcRenderer.on('main:process', (_, data) => {
  DUTY_STATUS = data
})

interface msgAttributes {
  id: string
  role: string
  content: string
}

let goodList: Array<any> = []
let goodDetailList: Array<any> = []
let isGetGoods = false // 是否获取商品列表
;(async () => {
  isGetGoods = (await ipcRenderer.invoke('get-conf', `${VIEWID}.isGetGoods`)) || false
  __log('lisgetgoods', isGetGoods)
})()
let getGoodsState = 0 // 获取商品列表状态, 0: 未开始, 1: 正在获取, 2: 获取完成

ipcRenderer.on('getGoods', (_, data) => {
  isGetGoods = data
  ipcRenderer.send('set-conf', `${VIEWID}.isGetGoods`, isGetGoods)
  getGoodsState = 0
  location.href = 'https://s.kwaixiaodian.com/'
})

// 产品采集
async function getGoodList(page = 1) {
  if (!loginState || !location.href.includes('https://s.kwaixiaodian.com/')) return
  getGoodsState = 1
  ipcRenderer.send('collection:state', { viewId: VIEWID, state: getGoodsState })
  const res = await fetch(
    'https://s.kwaixiaodian.com/rest/pc/product/manage/kcf/item/manager/queryList',
    {
      headers: {
        accept: 'application/json',
        'accept-language': 'zh-CN',
        'content-type': 'application/json',
        kpf: 'PC_WEB',
        kpn: 'KWAIXIAODIAN',
        priority: 'u=1, i',
        'sec-ch-ua': '"Not:A-Brand";v="24", "Chromium";v="134"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin'
      },
      referrer: 'https://s.kwaixiaodian.com/zone/goods/v1/list',
      referrerPolicy: 'no-referrer-when-downgrade',
      body: `{\"managerTab\":\"ALL\",\"pagination\":{\"curPage\":${page},\"pageSize\":50},\"queryForm\":{\"soldQuantityPeriodQuery\":\"\",\"itemProfileQuery\":\"normal\"},\"tableSort\":{\"volumeFilter\":\"\"}}`,
      method: 'POST',
      mode: 'cors',
      credentials: 'include'
    }
  )
  const data = await res.json()
  goodList = goodList.concat(data.data.dataSource)
  __log('商品列表：', goodList)
  if (data.data.total > goodList.length) {
    await sleep(2000)
    await getGoodList(page + 1)
  } else {
    __log('商品列表获取完成')
    __log('商品列表：', goodList)
    await getGoodDetail()
  }
}

async function getGoodDetail() {
  for (let index = 0; index < goodList.length; index++) {
    await sleep(1000)
    const good = goodList[index]
    const res = await fetch(
      'https://s.kwaixiaodian.com/rest/pc/product/manage/item/publish/render',
      {
        headers: {
          accept: 'application/json',
          'accept-language': 'zh-CN',
          'content-type': 'application/json',
          kpf: 'PC_WEB',
          kpn: 'KWAIXIAODIAN',
          priority: 'u=1, i',
          'sec-ch-ua': '"Not:A-Brand";v="24", "Chromium";v="134"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"Windows"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-origin'
        },
        referrer: `https://s.kwaixiaodian.com/zone/goods/config/release/detail?itemId=${good.itemId}`,
        referrerPolicy: 'no-referrer-when-downgrade',
        body: `{\"step\":1,\"model\":\"detail\",\"itemId\":\"${good.itemId}\",\"isCopy\":false,\"profileBiz\":null,\"profileTopicId\":null}`,
        method: 'POST',
        mode: 'cors',
        credentials: 'include'
      }
    )
    const data = await res.json()
    if (data.error_msg == '成功' && data.result == 1) {
      goodDetailList.push(parseGoods(data.data))
    }
  }
  __log('商品详情：', goodDetailList)
  getGoodsState = 2

  ipcRenderer.send('collection:state', { viewId: VIEWID, state: getGoodsState })
  ipcRenderer.send('set-conf', `${VIEWID}.isGetGoods`, false)
  location.reload()
}

function parseGoods(good) {
  __log(good)
  let goodDesc = ''
  goodDesc += `产品ID： ${good.global.itemId}\n`
  goodDesc += `产品标题：${good.data.title.fields.detailValue}\n`
  goodDesc += `产品断标题：${good.data.shortTitle.fields.detailValue}\n`
  goodDesc += `产品分类：${good.global.categoryNamePath.join('>')}\n`
  if (good.data.mainImage)
    goodDesc += `产品主图：${JSON.stringify(good.data.mainImage.fields.detailValue)}\n`

  if (good.data.decorate)
    goodDesc += `产品详情图：${JSON.stringify(good.data.decorate.fields.detailValue)}\n`

  if (good.data.goodsAttrs) {
    let goodsAttrs = good.data.goodsAttrs.fields.detailValue
    let goodsAttrsDesc: Array<string> = []
    for (let i = 0; i < goodsAttrs.length; i++) {
      const _attr = goodsAttrs[i]
      for (let j = 0; j < _attr.list.length; j++) {
        const attr = _attr.list[j]
        let value = attr.value
        if (Array.isArray(value)) {
          value = value.join('、')
        }
        goodsAttrsDesc.push(`${attr.label}：${value}`)
      }
    }

    goodDesc += `产品属性：${JSON.stringify(goodsAttrsDesc)}\n`
  }

  if (good.data.skuList) {
    goodDesc += `产品选项：\n`
    for (let j = 0; j < good.data.skuList.fields.detailValue.length; j++) {
      let item = good.data.skuList.fields.detailValue[j]

      let prop = ''
      for (let i = 0; i < item.relationSpecificationList.length; i++) {
        const spec = item.relationSpecificationList[i]
        prop += `${spec.propName}>${spec.propValueName}`
        if (spec.propValueRemarks) {
          prop += `(${spec.propValueRemarks})`
        }
        prop += '，'
      }

      goodDesc += `\t规格：${prop}`
      if (item.skuNo) goodDesc += `规格编码：${item.skuNo}，`
      goodDesc += `数量：${item.skuStock}，`
      goodDesc += `单买价格：${item.skuSalePrice / 100}，`
      goodDesc += `参考价格：${item.skuMarketPrice / 100}，`
      goodDesc += `规格图片：${item.imageUrl}\n`
    }
  }

  return goodDesc
}
// 产品采集-end

var loginState: boolean | undefined = undefined

// 监听DOM加载完成
document.addEventListener('DOMContentLoaded', () => {
  __log('DOM加载完成')
  // 全局变量
  var shopName: string | undefined = undefined

  var msgManager: Array<msgAttributes> = [] // 聊天记录

  var processedMessages = new Set() // 已处理的消息ID集合，用于去重

  var currentUser: string = '' // 当前用户

  var messageAiStarts = false // 是否开始回复

  var timeOut: any = null // 超时定时器

  var userStopNum = 0

  // 获取店铺名称
  async function getShopName() {
    const res = await fetch('https://s.kwaixiaodian.com/rest/app/tts/seller/login/info', {
      headers: {
        accept: 'application/json',
        'accept-language': 'zh-CN',
        priority: 'u=1, i',
        'sec-ch-ua': '"Not:A-Brand";v="24", "Chromium";v="134"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin'
      },
      referrer: 'https://s.kwaixiaodian.com/zone/order/list',
      referrerPolicy: 'no-referrer-when-downgrade',
      body: null,
      method: 'GET',
      mode: 'cors',
      credentials: 'include'
    })
    const data = await res.json()
    const res1 = await fetch('https://s.kwaixiaodian.com/pass/kshop/login/multiAccountChangeList', {
      headers: {
        accept: '*/*',
        'accept-language': 'zh-CN',
        'cache-control': 'max-age=0',
        'content-type': 'multipart/form-data; boundary=----WebKitFormBoundaryxg2CzzQduZC5FNdk',
        priority: 'u=1, i',
        'sec-ch-ua': '"Not:A-Brand";v="24", "Chromium";v="134"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin'
      },
      referrer: 'https://s.kwaixiaodian.com/zone/sub-account/change',
      referrerPolicy: 'no-referrer-when-downgrade',
      body: '------WebKitFormBoundaryxg2CzzQduZC5FNdk\r\nContent-Disposition: form-data; name="countryCode"\r\n\r\n+86\r\n------WebKitFormBoundaryxg2CzzQduZC5FNdk\r\nContent-Disposition: form-data; name="sid"\r\n\r\nkshop.api\r\n------WebKitFormBoundaryxg2CzzQduZC5FNdk--\r\n',
      method: 'POST',
      mode: 'cors',
      credentials: 'include'
    })
    const data1 = await res1.json()
    let shopName = ''
    for (let i = 0; i < data1.userInfos.length; i++) {
      const item = data1.userInfos[i]
      if (data.userInfo.userId == item.userId) {
        shopName = item.kshopName
        break
      }
    }
    return shopName
  }

  // 检查登录状态
  async function checkLoginStatus() {
    if (
      location.href.includes('www.kwaixiaodian.com') ||
      location.href.includes('login.kwaixiaodian.com')
    ) {
      loginState = false
    } else {
      loginState = true
    }
    // __log('VIEWID：', VIEWID)
    // __log('登录状态2：', loginState)
    window.api.setLoginStatus(loginState)
    if (location.host == 's.kwaixiaodian.com') {
      // 获取微信小店店铺名称
      const shopName = await getShopName()
      if (shopName) {
        window.api.setShopName(shopName)
      }
      if (!isGetGoods) {
        // 跳转到客服页面
        __log('跳转到客服页面')
        window.location.href = 'https://im.kwaixiaodian.com/'
      }
    } else {
      if (isGetGoods) {
        location.href = 'https://s.kwaixiaodian.com/'
      }
    }
  }

  function getChatMessages() {
    // 获取聊天记录
    var msgList: any = []
    var hasNewMessages: boolean = false
    if (location.host == 'im.kwaixiaodian.com') {
      const msgItems = document.querySelectorAll('.cs-message-matcher-card-wrapper')
      for (let i = 0; i < msgItems.length; i++) {
        const item = msgItems[i]
        const isMe = item.querySelector('.kwaishop-cs-LayoutDefaultWrapper__isMine') // 判断是否为客服消息
        const msgId = item.id

        if (!processedMessages.has(msgId)) {
          hasNewMessages = true
          processedMessages.add(msgId)
          // 文本消息
          const textMsg = item.querySelector('.kwaishop-cs-BizTextCard')
          if (textMsg) {
            const msgText = textMsg.textContent || ''
            msgList.push({
              role: isMe ? 'assistant' : 'user',
              id: msgId,
              content: msgText
            })
            continue
          }

          // 图片消息
          const imgMsg = item.querySelector('img.message-image')
          if (imgMsg) {
            const imgSrc = (imgMsg as HTMLImageElement).src
            msgList.push({
              role: isMe ? 'assistant' : 'user',
              id: msgId,
              content: `这是一个图片，${imgSrc}`
            })
            continue
          }

          // 产品卡片
          const productCard = item.querySelector('.kwaishop-cs-LayoutDefaultWrapper_msgBody')
          if (productCard && !isMe) {
            const productContainer = productCard.querySelector(
              '.kope-view[role="button"] .kope-view .kope-view'
            )
            if (productContainer) {
              // 提取商品名称
              const productName = productCard?.querySelectorAll(
                '.kope-text--overflow-hidden.kope-text--singleline'
              )[0]?.textContent

              // 提取价格（组合所有价格相关的span）
              const priceElements = productCard.querySelectorAll('.kope-text')
              let price = ''
              // 遍历所有文本元素，组合价格部分
              priceElements.forEach((el: any, index: number) => {
                if (index > 3 && index < 7) {
                  const text = el.textContent.trim()
                  if (text === '¥' || text === '.' || !isNaN(parseFloat(text))) {
                    price += text
                  }
                }
              })
              if (priceElements[0]?.textContent?.includes('商品')) {
                price = price.replace(/\s+/g, '')
                msgList.push({
                  // role: isMe ? 'assistant' : 'user',
                  role: 'system',
                  id: msgId,
                  content: `以下是可供参考的商品信息，请根据用户问题精准回答：\n商品名称：${productName}，商品价格：${price}`
                })
              }
            }
          }
        }
      }
      if (hasNewMessages) {
        // 有新消息时
        msgManager = [...msgManager, ...msgList]
        if (msgList.length > 0) {
          // messages.push({
          //   role:'user',
          //   content:'你好，请问这个商品有吗'
          // })
          if (msgManager[msgManager.length - 1].role == 'user') {
            handleProcess(msgManager)
          }
        }
      } else {
        // __log('没有新消息',msgManager)
      }
    }
  }

  async function handleProcess(message: any) {
    // 获取AI回复
    // if(!aiKfStatus) return
    let msgList = message
    __log('msgList', msgList[msgList.length - 1].role)
    if (msgList.length && msgList[msgList.length - 1].role == 'user') {
      messageAiStarts = true //AI开始生成消息
      const res = await ipcRenderer.invoke('kuaishou:chat:process', {
        shopName,
        msgList,
        kbName: KNOWLEDGE,
        viewId: VIEWID
      })
      const data = JSON.parse(res)
      __log('AI回复', data)
      if (data.choices[0].message.content) {
        let text = data.choices[0].message.content
        text = removeThinking(text)
        sendMessage(text)
      } else {
        messageAiStarts = false
      }
    }
  }

  function sendMessage(msg: string) {
    // 发送消息
    const chunkSize = 800 // 每次发送的消息长度
    const result: string[] = []
    __log('发送消息', msg)
    messageAiStarts = false
    for (let i = 0; i < msg.length; i += chunkSize) {
      result.push(msg.slice(i, i + chunkSize))
    }
    for (let i = 0; i < result.length; i++) {
      const textarea = document.querySelector('.ql-editor p')
      if (textarea) {
        textarea.innerHTML = result[i]
        // 点击发送按钮
        const enterKeyDownEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13, // 传统属性，已废弃但仍被广泛支持
          which: 13, // 传统属性，已废弃但仍被广泛支持
          bubbles: true, // 事件是否冒泡
          cancelable: true // 事件能否被取消
        })
        textarea?.dispatchEvent(enterKeyDownEvent)
        messageAiStarts = false
        userStopNum = 0
      }
    }
  }

  function getLeftUserList() {
    // 获取左侧用户列表
    __log('查找新消息:', userStopNum)
    if (userStopNum > 0) {
      userStopNum--
      __log('点过了:', userStopNum)
      return
    }
    const userList = document.querySelectorAll('.SessionStarCard')
    userList.forEach((item, _) => {
      // if(index == 4){
      //   if(messageAiStarts) return
      //   let userClick = item.querySelector('.SessionBaseCard-Static')
      //   if(userClick){
      //     let name = userClick.querySelector('.SessionBaseCard-topHeadName')?.textContent

      //     if(currentUser == name) return

      //     const mouseEvent = new MouseEvent('click', {
      //       view: window,
      //       bubbles: true,
      //       cancelable: true
      //     });
      //     userClick?.dispatchEvent(new MouseEvent('mouseover', { bubbles: true }));
      //     userClick?.dispatchEvent(new MouseEvent('mousemove', { bubbles: true }));
      //     setTimeout(() => {
      //       __log('点击用户',index)

      //       currentUser = name||''
      //       msgManager = []
      //       userClick?.dispatchEvent(mouseEvent);
      //       userStopNum = 3
      //     }, 300)
      //   }
      // }
      // return
      let newUser = item.querySelector('.SessionBaseCard-unread-rot')
      if (newUser) {
        if (messageAiStarts) return
        let userClick = item.querySelector('.SessionBaseCard-Static')
        let name = userClick?.querySelector('.SessionBaseCard-topHeadName')?.textContent
        if (currentUser == name) return
        // 有新消息直接点击
        const mouseEvent = new MouseEvent('click', {
          view: window,
          bubbles: true,
          cancelable: true
        })
        userClick?.dispatchEvent(new MouseEvent('mouseover', { bubbles: true }))
        userClick?.dispatchEvent(new MouseEvent('mousemove', { bubbles: true }))

        clearTimeout(timeOut)
        timeOut = setTimeout(() => {
          currentUser = name || ''
          userClick?.dispatchEvent(mouseEvent)
          processedMessages = new Set()
          msgManager = []
          userStopNum = 3
        }, 300)
        return
      }
    })
  }

  const main = async () => {
    while (true) {
      await checkLoginStatus()
      if (isGetGoods) {
        await getGoodList()
      }
      if (loginState && DUTY_STATUS) {
        getChatMessages()
        getLeftUserList()
      }
      await sleep(2000)
    }
  }
  main()
})
