import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { log, sleep } from './utils'

const api = {}

// 使用`contextBridge` api将Electron api暴露给
// 只有在启用上下文隔离时才渲染，否则
// 添加到DOM全局变量
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore（在dts中定义）
  window.electron = electronAPI
  // @ts-ignore（在dts中定义）
  window.api = api
}

let VIEWID = ''
let DUTY_STATUS = false // 负责状态
let KNOWLEDGE = '' // 知识库
console.log(KNOWLEDGE);


ipcRenderer.on('view:id', (_, data) => {
  VIEWID = data.viewId
  KNOWLEDGE = data.knowledge
  DUTY_STATUS = data.dutyStatus
  localStorage.setItem('viewId', VIEWID)
  localStorage.setItem('dutyStatus', data.dutyStatus)
})

ipcRenderer.on('main:process', (_, data) => {
  DUTY_STATUS = data
})

// 监听DOM加载完成
document.addEventListener('DOMContentLoaded', () => {
  const main = async (): Promise<void> => {
    while (true) {
      if (DUTY_STATUS) {
        log('开始检查登录状态')
      } else {
        await sleep(2000)
      }
    }
  }
  main()
})
