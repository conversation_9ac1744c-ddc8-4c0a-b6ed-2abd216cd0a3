const nativeConsole = Object.assign({}, window.console);
import _log from 'electron-log'

// 全选输入框内容
function selectAllText(element: Node) {
  const range = document.createRange()
  range.selectNodeContents(element)
  const selection = window.getSelection()
  selection?.removeAllRanges()
  selection?.addRange(range)
  selection?.collapseToEnd()
}

// 插入文本
function insertTextAtCursor(text: string) {
  const selection = window.getSelection()
  if (selection?.rangeCount) {
    const range = selection.getRangeAt(0)
    range.deleteContents()
    range.insertNode(document.createTextNode(text))
    // 移动光标到插入内容末尾
    range.setStartAfter(range.endContainer)
    selection.removeAllRanges()
    selection.addRange(range)
  }
}

// 设置输入框内容
export function setInput(selector: HTMLTextAreaElement | HTMLInputElement, text: string): void {
  selector.focus()
  selectAllText(selector)
  insertTextAtCursor(text)
  selector.dispatchEvent(new Event('input', { bubbles: true }))
}

 // 设置输入框内容
export function setTextareaText(selector: HTMLTextAreaElement, text: string): void {
  if (selector.setRangeText) {
    selector.setRangeText(text)
    selector.selectionStart = selector.selectionEnd = selector.value.length
  }
  selector.dispatchEvent(
    new InputEvent('input', {
      bubbles: true,
      data: text
    })
  )
}

// 日志
export function log(...arg) {
  _log.info(...arg)
  nativeConsole.log(`platformScript`, ...arg)
}

// 等待元素
export async function waitForElement(selector: string, timeout = 5000) {
  const start = Date.now()
  return new Promise((resolve, reject) => {
    function check() {
      const el = document.querySelector(selector)
      if (el) {
        resolve(el)
      } else if (Date.now() - start >= timeout) {
        reject(null)
      } else {
        setTimeout(check, 100)
      }
    }
    check()
  })
}


// 去除ai回复的思考过程
export function removeThinking(text: string) { 
  const result = text.replace(/<think>[\s\S]*?<\/think>/g, '');
  return result;
}

// 线程休眠
export function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}