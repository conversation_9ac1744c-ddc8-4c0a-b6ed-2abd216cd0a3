import { ElectronAPI } from '@electron-toolkit/preload'

declare global {
  interface Window {
    electron: ElectronAPI
    api: {
      quit: () => void
      maximize: (isMaximized: boolean) => void
      minimize: () => void
      createView: ({
        shopId: string,
        knowledge: string,
        userName: string,
        password: string,
        shopName: string,
        promptName: string
      }) => Promise<any>
      getViews: () => Promise<any>
      getShop: () => Promise<any>
      selectView: (id: string) => void
      closeView: (viewId: string) => Promise<any>
      switchDuty: (viewId: string) => Promise<any>
      update: (callback: () => void) => void
      getKnowledgeBasesList: () => Promise<any>
      setViewKnowledge: (viewId: string, knowledge: string) => void
      getConfig: () => AppConfig
      setConfig: (newConfig: Partial<AppConfig>) => Promise<any>
      updateView: (
        viewId: string,
        knowledge: string,
        userName: string,
        password: string,
        shopName: string,
        promptName: string
      ) => Promise<any>
      getPromptTemplateList: (data) => Promise<any>
      addShopKnowledge: (data) => Promise<any>
      setShopKnowledge: (data) => Promise<any>
      warning: (callback: (msg: string) => void) => void
      getModelListApi: (data) => Promise<any>
      getLog: () => Promise<any>
      startCollect: (viewId) => void
    }
  }
}
