import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { AppConfig } from '../types/config'

let position = {
  x: 0,
  y: 0,
  width: 0,
  height: 0
}

function setPosition(): void {
  // 获取窗口位置
  const element = document.getElementById('embed-container')
  if (element) {
    const rect = element.getBoundingClientRect()
    position = {
      x: rect.left,
      y: rect.top,
      width: rect.width,
      height: rect.height
    }
  } else {
    const rightWrapper = document.querySelector('.right-wrapper')
    if (rightWrapper) {
      const rect = rightWrapper.getBoundingClientRect()
      position = {
        x: rect.left,
        y: rect.top,
        width: rect.width,
        height: rect.height
      }
    }
  }
}

// 自定义渲染api
const api = {
  quit: () => ipcRenderer.send('quit-app'),
  maximize: (isMaximized: boolean) => ipcRenderer.send('maximize-app', isMaximized),
  minimize: () => ipcRenderer.send('minimize-app'),
  createView: ({ shopId, knowledge, userName, password, shopName, promptName }) => {
    if (position.width == 0) {
      setPosition()
    }
    return ipcRenderer.invoke(
      'view:create',
      shopId,
      knowledge,
      position,
      userName,
      password,
      shopName,
      promptName
    )
  },
  getViews: () => ipcRenderer.invoke('view:list'),
  getShop: () => ipcRenderer.invoke('shop:list'),
  selectView: (id: string) => {
    if (position.width == 0) {
      setPosition()
    }
    ipcRenderer.send('view:top', id, position)
  },
  closeView: (viewId: string) => ipcRenderer.invoke('view:delete', viewId),

  switchDuty: (viewId: string) => {
    ipcRenderer.send('view:switchDuty', viewId)
    return ipcRenderer.invoke('view:switchDuty', viewId)
  },

  update: (callback) =>
    ipcRenderer.on('update', () => {
      callback && callback()
    }),

  getKnowledgeBasesList: () => ipcRenderer.invoke('knowledgeBase:list'),

  setViewKnowledge: (viewId: string, knowledge: string) =>
    ipcRenderer.send('view:update:knowledge', viewId, knowledge),
  getConfig: () => ipcRenderer.invoke('get-config'),
  setConfig: (newConfig: Partial<AppConfig>) => ipcRenderer.invoke('set-config', newConfig),
  updateView: (
    viewId: string,
    konwledge: string,
    userName: string,
    password: string,
    shopName: string,
    promptName: string
  ) =>
    ipcRenderer.invoke('view:update', {
      viewId,
      userName,
      password,
      konwledge,
      shopName,
      promptName
    }),
  getPromptTemplateList: (data) => ipcRenderer.invoke('call:api', 'getPromptTemplateListApi', data),
  warning: (callback) => {
    ipcRenderer.on('warning', (_, message) => {
      callback && callback(message)
    })
  },
  getModelListApi: (data) => ipcRenderer.invoke('call:api', 'getModelListApi', data),
  getLog: () => ipcRenderer.invoke('log:get'),
  startCollect: (viewId) => ipcRenderer.send('collection:start', viewId)
}

ipcRenderer.on('get-element-position', () => {
  // 获取窗口位置
  let position = {
    x: 0,
    y: 0,
    width: 0,
    height: 0
  }
  const element = document.getElementById('embed-container')
  if (element) {
    const rect = element.getBoundingClientRect()
    position = {
      x: rect.left,
      y: rect.top,
      width: rect.width,
      height: rect.height
    }
  }
  ipcRenderer.send('element-position', position)
})

// 使用`contextBridge` api将Electron api暴露给
// 只有在启用上下文隔离时才渲染，否则
// 添加到DOM全局变量
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore（在dts中定义）
  window.electron = electronAPI
  // @ts-ignore（在dts中定义）
  window.api = api
}

window.addEventListener('resize', () => {
  setPosition()
})
