import { DataTypes, Model } from 'sequelize'
import { sequelize } from '../database'

interface ViewAttributes {
  id?: number
  name: string
  viewId: string
  isDuty?: boolean
  loginState?: boolean
  isDelet?: boolean
  deletDate?: Date | null
  shopId: number
  knowledge?: string | ''
  userName?: string | ''
  password?: string | ''
  promptName?: string | ''
  collectionState?: number | 0
}

class View extends Model<ViewAttributes> implements ViewAttributes {
  declare id: number
  declare name: string
  declare viewId: string
  declare isDuty: boolean
  declare loginState: boolean
  declare isDelet: boolean
  declare deletDate: Date | null
  declare shopId: number
  declare knowledge: string
  declare userName: string | ''
  declare password: string | ''
  declare promptName: string | ''
  declare collectionState: number
}

View.init(
  {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    name: { type: DataTypes.STRING, allowNull: false },
    viewId: { type: DataTypes.STRING, unique: true },
    isDuty: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    loginState: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    isDelet: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    deletDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    shopId: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    knowledge: {
      type: DataTypes.TEXT,
      defaultValue: ''
    },
    userName: {
      type: DataTypes.TEXT,
      defaultValue: ''
    },
    password: {
      type: DataTypes.TEXT,
      defaultValue: ''
    },
    promptName: {
      type: DataTypes.TEXT,
      defaultValue: ''
    },
    collectionState: {
      type: DataTypes.NUMBER,
      defaultValue: 0
    }
  },
  { sequelize }
)

// View.sync({ force: true })
View.sync({ alter: true })
  .then(() => console.log('Model synced'))
  .catch((err) => console.error('Sync failed:', err))

export default View
