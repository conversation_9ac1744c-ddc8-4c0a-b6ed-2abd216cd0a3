import { Sequelize } from 'sequelize';
import path from 'path';

// 获取应用程序的根目录路径
const rootPath = process.env.NODE_ENV === 'development'
  ? path.join(__dirname, '..')
  : path.join(process.resourcesPath, 'app.asar.unpacked');

// 在根目录下存储SQLite文件
const dbPath = path.join(rootPath, 'app-data.sqlite');

export const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: dbPath,
  logging: false
});

// 测试连接
sequelize.authenticate()
  .then(() => console.log('Database connected'))
  .catch(err => console.error('Connection failed:', err));
