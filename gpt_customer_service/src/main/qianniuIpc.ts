import { ipc<PERSON>ain } from 'electron'
import { ViewService } from '../main/services/viewService'
import { KbChatApi } from '../api/chat'
import { getConfig } from '../store/configStore'
import log from 'electron-log'

export function qianniuIpc(_) {
  ipcMain.on('qianniu:login:status', async (_, data) => {
    await ViewService.updateViewLoginStatus(data.viewId, data.status)
  })
  ipcMain.on('qianniu:shop:name', async (_, data) => {
    await ViewService.updateViewName(data.viewId, data.shopName)
  })
  ipcMain.handle('qianniu:chat:process', async (_, data) => {
    const view = await ViewService.getViewKnowledge(data.viewId)
    const kbName = view?.knowledge
    const promptName = view?.promptName
    
    let query = ""
    for (let i = 0; i < data.msgList.length; i++) {
      let item = data.msgList[i]
      if (item.role === "user") {
        query = item.content
      }
    }
    try { 
      const config = getConfig();
      const response = await KbChatApi({
        "query": query,
        "kb_name": kbName,
        "history": data.msgList,
        "stream": false,
        "model": config.modelName,
        "prompt_name": promptName
      })
      return response
    } catch (error) {
      log.info(error)
      return null
    }
  })
}
