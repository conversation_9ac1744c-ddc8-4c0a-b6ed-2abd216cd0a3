import { ipcMain } from 'electron'

export function testIpc() {
  ipcMain.handle('receiveData', async (_, data) => {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: 'Bearer 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',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'google/gemma-3n-e4b-it:free',
        messages: data,
      }),
    });
    return await response.json();
  })
}
