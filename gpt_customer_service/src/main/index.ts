import { app, shell, <PERSON><PERSON>erWindow, ipc<PERSON>ain, WebContentsView, session, Tray, Menu } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import { testIpc } from './testIpc'
import { douyinIpc } from './douyinIpc'
import { qianniuIpc } from './qianniuIpc'
import { pinduoduoIpc } from './pinduoduoIpc'
import { weixinIpc } from './weixinIpc'
import { kuaishouIpc } from './kuaishouIpc'
import { registerServices } from './services'
import { ViewService } from './services/viewService'
import { v4 as uuidv4 } from 'uuid'
import View from '../models/View'
import fs from 'fs-extra'
import { listKnowledgeBasesApi } from '../api/knowledgeBase'
import { getConfig, setConfig } from '../store/configStore'
import api from '../api/api'
// import contextMenu from 'electron-context-menu'
import { existsSync, mkdirSync } from 'fs'
import log from 'electron-log'
import { Conf } from 'electron-conf/main'


const contextMenu = require('electron-context-menu').default

// 设置日志文件路径（默认在应用数据目录）
log.transports.file.level = 'info'
log.transports.file.format = '{y}-{m}-{d} {h}:{i}:{s} {level} {text}'

process.on('unhandledRejection', (err) => {
  log.error('Unhandled rejection:', err)
  // 可添加邮件通知或日志记录逻辑
})

const config = getConfig()
// 全局视图管理器
const conf = new Conf()
global.viewManager = {
  views: new Map<string, { instance: WebContentsView }>()
}
global.userAgent =
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'

const isValidPartition = (name: string) => {
  try {
    return !!session.fromPartition(name).cookies
  } catch {
    return false
  }
}

function createWindow(): BrowserWindow {
  const mainWindow = new BrowserWindow({
    width: 1600,
    height: 900,
    minWidth: 1400,
    minHeight: 800,
    show: false,
    frame: false,
    autoHideMenuBar: true,
    transparent: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR用于基于electron-vite cli的渲染器。
  // 加载用于开发的远程URL或用于生产的本地html文件。
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    log.info('Loading remote URL:', process.env['ELECTRON_RENDERER_URL'])

    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
  return mainWindow
}

function initHistoryWebView(mainWindow: BrowserWindow) {
  const partitionsPath = join(app.getPath('userData'), 'gpt_customer_service', 'Partitions')
  if (!existsSync(partitionsPath)) {
    mkdirSync(partitionsPath, { recursive: true }) // 递归创建目录
  }
  var files = Array<string>()

  fs.readdirSync(partitionsPath).forEach((file) => {
    files.push(file)
  })

  // 初始化数据库已有的视图
  ViewService.listViews().then((res) => {
    res?.forEach((view: View & { Shop?: any }) => {
      const viewId = view.viewId
      const fileName = viewId.replace('persist:', '')
      if (files.includes(fileName)) {
        files = files.filter((file) => file !== fileName)
      }
      if (isValidPartition(viewId)) {
        const position = {
          x: 0,
          y: 0,
          width: 0,
          height: 0
        }

        const shop = SHOP_LIST.find((shop) => shop.id === view.shopId)
        if (shop) {
          const preloadJs = shop?.preloadJs ?? void 0
          const webView = new WebContentsView({
            webPreferences: {
              devTools: true, // 必须开启
              contextIsolation: false, // 允许预加载脚本访问DOM
              partition: viewId, // 每个视图使用不同的存储分区
              preload: preloadJs ? join(__dirname, preloadJs) : void 0,
              sandbox: false,
              backgroundThrottling: false
            }
          })
          // 加载完成后打开控制台
          webView.webContents.on('did-finish-load', () => {
            if (is.dev || config.debug) {
              webView.webContents.openDevTools({ mode: 'detach' })
            }
            ViewService.getViewDutyStatus(viewId).then((row) => {
              let data = {
                viewId: viewId,
                knowledge: row?.dataValues.knowledge,
                dutyStatus: row?.dataValues.isDuty,
                userName: row?.dataValues.userName,
                password: row?.dataValues.password
              }
              webView.webContents.send('view:id', data)
            })
          })

          initWebView(webView)

          mainWindow.contentView.addChildView(webView)
          webView.webContents.loadURL(shop.url)
          webView.setBounds(position)

          global.viewManager.views.set(viewId, webView)
        }
      }
    })
    files.forEach((file) => {
      fs.removeSync(join(partitionsPath, file))
    })
  })
}

// 初始化WebView
function initWebView(_view: WebContentsView) {
  // 设置右键菜单
  contextMenu({
    window: _view,
    showSearchWithGoogle: false,
    showSelectAll: true,
    showSaveImageAs: true,
    showSaveVideoAs: true,
    showCopyLink: true,

    labels: {
      cut: '剪切',
      copy: '复制',
      paste: '粘贴',
      selectAll: '全选',
      copyImage: '复制图像',
      saveImageAs: '保存图像为…',
      saveVideoAs: '保存视频为…',
      copyLink: '复制链接',
      inspect: '检查元素'
    },
    prepend: (_, __, browserWindow) => [
      {
        label: '后退',
        enabled: browserWindow?.webContents.navigationHistory.canGoBack(),
        click: () => browserWindow?.webContents.navigationHistory.goBack()
      },
      { type: 'separator' }
    ]
  })

  _view.webContents.session.webRequest.onBeforeSendHeaders((details, callback) => {
    details.requestHeaders['User-Agent'] = global.userAgent // 自定义UA
    callback({ cancel: false, requestHeaders: details.requestHeaders })
  })

  _view.webContents.setUserAgent(global.userAgent)

  const urlList = [
    '/pc_seller/',
    '/pc_seller_v2/',
    '/morder/order/detail',
    '/chat-merchant/index.html',
    '/ffa/g/create'
  ]

  _view.webContents.setWindowOpenHandler((details) => {
    let _f = false
    urlList.forEach((url) => {
      if (details.url.includes(url)) {
        _f = true
      }
    })
    log.info(details.url)
    if (_f) {
      _view.webContents.loadURL(details.url)
    }
    return { action: 'deny' } // 阻止默认行为
  })

  const schemes = ['bytedance']
  schemes.forEach((scheme) => {
    _view.webContents.session.protocol.handle(scheme, () => new Response(null))
  })
}

interface Shop {
  id: number
  name: string
  icon: string
  url: string
  preloadJs: string
}

const SHOP_LIST: Shop[] = [
  {
    id: 1,
    name: '抖店飞鸽',
    icon: 'src/assets/icon/doudian.png',
    url: 'https://fxg.jinritemai.com/login/common',
    preloadJs: '../preload/platform_scripts/douyin_main.js'
  },
  {
    id: 2,
    name: '千牛天猫',
    icon: 'src/assets/icon/qianniu.png',
    url: 'https://loginmyseller.taobao.com/',
    preloadJs: '../preload/platform_scripts/qianniu.js'
  },
  {
    id: 3,
    name: '小红书千帆',
    icon: 'src/assets/icon/xiaohongshu.png',
    url: 'https://ark.xiaohongshu.com/ark',
    preloadJs: '../preload/platform_scripts/test.js'
  },
  {
    id: 4,
    name: '拼多多',
    icon: 'src/assets/icon/pingduoduo.png',
    url: 'https://mms.pinduoduo.com/login',
    preloadJs: '../preload/platform_scripts/pinduoduo.js'
  },
  {
    id: 5,
    name: '微信小店',
    icon: 'src/assets/icon/weixinxiaodian.png',
    url: 'https://store.weixin.qq.com',
    preloadJs: '../preload/platform_scripts/weixin_main.js'
  },
  {
    id: 6,
    name: '快手小店',
    icon: 'src/assets/icon/kuaishouxiaodian.png',
    url: 'https://login.kwaixiaodian.com/?biz=zone&redirect_url=https%3A%2F%2Fim.kwaixiaodian.com%2F',
    preloadJs: '../preload/platform_scripts/kuaishou_main.js'
  }
]

// 当Electron完成时，这个方法会被调用
// 初始化并准备创建浏览器窗口。
// 有些api只能在此事件发生后使用
app.whenReady().then(() => {
  global.sendWarning = function (msg: string = '') {
    mainWindow.webContents.send('warning', msg)
  }
  global.tray = new Tray(join(__dirname, '../../resources/icon.png'))
  const iconMenu = Menu.buildFromTemplate([
    { label: '打开', click: () => mainWindow.show() },
    { label: '退出', role: 'quit' }
  ])
  global.tray.setContextMenu(iconMenu)
  global.tray.setToolTip('后台运行中')
  global.tray.on('click', () => mainWindow.show())

  // global.tray.displayBalloon({
  //   title: "新消息",
  //   content: "您收到一条通知",
  //   iconType: 'info' // 或自定义图标
  // });

  const oldUa = session.defaultSession.getUserAgent()
  global.userAgent = oldUa
    .replace(/Electron\/\d*\.\d*\.\d*\s/, '')
    .replace(`${app.getName()}/${app.getVersion()} `, '') // 自定义UA
  session.defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
    details.requestHeaders['User-Agent'] = global.userAgent // 自定义UA
    callback({ cancel: false, requestHeaders: details.requestHeaders })
  })
  // 为windows设置app用户模型id
  electronApp.setAppUserModelId('com.electron')

  // 默认情况下，在开发环境中通过F12打开或关闭DevTools
  // 并且在生产环境中忽略CommandOrControl + R。
  // 参见https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
    // 在开发模式下自动打开开发者工具
    if (config.debug) {
      window.webContents.openDevTools()
    }
  })

  const mainWindow = createWindow()

  // 监听所有窗口关闭事件
  mainWindow.on('closed', () => {
    global.viewManager.views.forEach((view) => {
      view.webContents.session.flushStorageData()
    })
  })

  // 监听窗口大小变化事件
  mainWindow.on('resize', () => {
    mainWindow.webContents.send('get-element-position')
  })

  // 监听窗口位置变化事件
  ipcMain.on('element-position', (_, position) => {
    global.viewManager.views.forEach((view) => {
      if (view.getBounds().width != 0) {
        view.setBounds(position)
      }
    })
  })

  // 在macOS上，当单击dock图标并且没有其他窗口打开时，重新创建一个窗口。
  app.on('activate', function () {
    // 在macOS上，当执行
    // 单击dock图标，没有打开其他窗口。
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })

  // 退出应用
  ipcMain.on('quit-app', () => {
    mainWindow.hide()
    // global.viewManager.views.forEach((view) => {
    //   view.webContents.session.flushStorageData()
    // })
    // app.quit()
  })

  // 最小化窗口
  ipcMain.on('minimize-app', () => {
    BrowserWindow.getFocusedWindow()?.minimize()
  })

  // 最大化/还原窗口
  ipcMain.on('maximize-app', (_, isMaximized: boolean) => {
    const focusedWindow = BrowserWindow.getFocusedWindow()
    if (isMaximized) {
      focusedWindow?.unmaximize()
    } else {
      focusedWindow?.maximize()
    }
  })

  // 创建视图
  ipcMain.handle(
    'view:create',
    async (_, shopId, knowledge, position, userName, password, shopName, promptName) => {
      // 获取店铺信息
      const shop = SHOP_LIST.find((shop) => shop.id === shopId)
      const viewId = `persist:view_${uuidv4()}` // 生成唯一的viewId
      if (!shop) throw new Error('店铺不存在')
      const viewDb = await ViewService.createView({
        name: shop.name,
        viewId,
        shopId: shop.id,
        userName: userName,
        password: password,
        knowledge: knowledge,
        promptName: promptName
      })
      const view = new WebContentsView({
        webPreferences: {
          devTools: true, // 必须开启
          contextIsolation: false, // 允许预加载脚本访问DOM
          partition: viewId, // 每个视图使用不同的存储分区
          preload: join(__dirname, shop.preloadJs),
          sandbox: false,
          backgroundThrottling: false
        }
      })
      // 加载完成后打开控制台
      view.webContents.on('did-finish-load', () => {
        if (is.dev || config.debug) {
          view.webContents.openDevTools({ mode: 'detach' }) // 分离模式显示控制台
        }
        // view.webContents.postMessage('view:loaded')
      })
      await api.addShopKnowledgeApi({
        shop_name: shopName,
        view_id: viewId,
        prompt_name: promptName,
        knowledge_base: knowledge,
        description: ''
      })
      initWebView(view)

      mainWindow.contentView.addChildView(view)
      view.webContents.loadURL(shop.url)
      view.setBounds(position)
      global.viewManager.views.set(viewId, view)
      return { viewId: viewDb?.dataValues.id }
    }
  )

  // 置顶指定视图
  ipcMain.on('view:top', (_, viewId, position) => {
    const { views } = global.viewManager
    if (viewId != '-1' && !views.has(viewId)) return
    views.forEach((view, id) => {
      if (id === viewId) {
        view.setBounds(position)
      } else {
        view.setBounds({ x: 0, y: 40, width: 0, height: 0 })
      }
    })
  })

  // 删除视图
  ipcMain.handle('view:delete', (_, viewId: string) => {
    const view = global.viewManager.views.get(viewId)
    if (view) {
      session.fromPartition(viewId).clearStorageData()

      view.webContents.close()
      view.webContents.closeDevTools()
      mainWindow.contentView.removeChildView(view)
      global.viewManager.views.delete(viewId)

      ViewService.deleteView(viewId)
    }
  })

  // 获取指定视图
  ipcMain.handle('get-browser-view', (_, viewId) => {
    return global.viewManager.views.get(viewId)
  })

  // 获取知识库列表
  ipcMain.handle('knowledgeBase:list', async () => {
    return await listKnowledgeBasesApi()
  })

  // 更新知识库
  ipcMain.on('view:update:knowledge', (_, viewId, knowledge) => {
    ViewService.updateViewKnowledge(viewId, knowledge)
  })

  // 返回商店列表
  ipcMain.handle('shop:list', () => SHOP_LIST)

  // 返回视图列表
  ipcMain.handle('view:list', async () => {
    const dbViews = await ViewService.listViews()

    dbViews?.map((view) => {
      view['Shop'] = SHOP_LIST.find((shop) => shop.id === view.shopId)
    })

    return dbViews
  })

  // 更新视图账户密码等信息
  ipcMain.handle(
    'view:update',
    async (_, { viewId, konwledge, userName, password, shopName, promptName }) => {
      try {
        await api.setShopKnowledgeApi({
          shop_knowledge_id: 0,
          view_id: viewId,
          shop_name: shopName,
          prompt_name: promptName,
          knowledge_base: konwledge,
          description: ''
        })
      } catch (error) {
        log.info(error)
      }
      return await ViewService.updateView(viewId, konwledge, userName, password, promptName)
    }
  )

  // 全局配置管理
  ipcMain.handle('get-config', () => getConfig())
  ipcMain.handle('set-config', (_, newConfig) => setConfig(newConfig))

  // 调用api
  ipcMain.handle('call:api', async (_, apiName, data) => {
    try {
      return await api[apiName](data)
    } catch (error) {
      log.error(error)
      return error
    }
  })

  ipcMain.on('collection:start', (_, VIEWID) => {
    global.viewManager.views.get(VIEWID)?.webContents.send('getGoods', true)
  })

  ipcMain.on('collection:state', (_, {viewId, state}) => {
    ViewService.updateViewCollectionState(viewId, state)
  })

  ipcMain.on('set-conf', (_, key, val) => {
    conf.set(key, val);
  });

  ipcMain.handle('get-conf', (_, key) => {
    return conf.get(key);
  });

  // 获取日志
  ipcMain.handle('log:get', () => {
    const logPath = log.transports.file.getFile().path
    const logText = fs.readFileSync(logPath, { encoding: 'utf-8' })
    return logText
  })

  registerServices(mainWindow)
  initHistoryWebView(mainWindow)
  douyinIpc(mainWindow)
  qianniuIpc(mainWindow)
  pinduoduoIpc(mainWindow)
  weixinIpc(mainWindow)
  kuaishouIpc(mainWindow)
  testIpc()
})

// 当所有窗口都关闭时退出，除了macOS。在那里，这很常见
// 让应用及其菜单栏在用户退出前保持活跃状态
// 显式使用Cmd + Q。
app.on('window-all-closed', () => {
  global.viewManager.views.forEach((view) => {
    view.webContents.session.flushStorageData()
  })
  global.tray.destroy()

  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 在这个文件中，你可以包含应用程序特定主进程的其余部分
// 代码。你也可以把它们放在单独的文件中，然后在这里引用它们。
