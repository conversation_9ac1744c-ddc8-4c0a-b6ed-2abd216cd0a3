import { ipcMain } from 'electron'
import { ViewService } from './services/viewService'

export function registerServices(_) {
  // 切换Duty
  ipcMain.handle('view:switchDuty', (_, viewId: string) => {
    const view = global.viewManager.views.get(viewId)
    if (view) {
      ViewService.getViewDutyStatus(viewId).then((row) => {
        view.webContents.send('main:process', row?.dataValues.isDuty)
      })
    }
    return ViewService.toggleViewDuty(viewId)
  })
}
