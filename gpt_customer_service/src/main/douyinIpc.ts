import { ipcMain } from 'electron'
import { ViewService } from '../main/services/viewService'
import { KbChatApi } from '../api/chat'
import { getConfig } from '../store/configStore';

export function douyinIpc(_) {
  ipcMain.on('douyin:login:status', async (_, data) => {
    await ViewService.updateViewLoginStatus(data.viewId, data.status)
  })
  ipcMain.on('douyin:shop:name', async (_, data) => {
    await ViewService.updateViewName(data.viewId, data.shopName)
  })
  ipcMain.handle('douyin:view:duty', async (_, viewId) => {
    const row = await ViewService.getViewDutyStatus(viewId)
    if (row) {
      return row.dataValues.isDuty
    } else {
      return false
    }
  })
  ipcMain.handle('douyin:chat:process', async (_, data) => {
    let kbName = data.kbName
    if (data.kbName === "") {
      const view = await ViewService.getViewKnowledge(data.viewId)
      kbName = view?.knowledge
    }
    let query = ""
    for (let i = 0; i < data.msgList.length; i++) {
      let item = data.msgList[i]
      if (item.role === "user") {
        query = item.content
      }
    }
    try { 
      const config = getConfig();
      const response = await KbChatApi({
        "view_id": data.viewId,
        "query": query,
        "kb_name": kbName,
        "history": data.msgList,
        "stream": false,
        "model": config.modelName,
      })
      return response
    } catch (error) {
      return null
    }
  })
}
