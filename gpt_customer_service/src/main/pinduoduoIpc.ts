import { ipcMain } from 'electron'
import { ViewService } from '../main/services/viewService'
import { KbChatApi } from '../api/chat'
import { getConfig } from '../store/configStore'
import log from 'electron-log'

export function pinduoduoIpc(_) {
  ipcMain.on('pinduoduo:login:status', async (_, data) => {
    await ViewService.updateViewLoginStatus(data.viewId, data.status)
  })
  ipcMain.on('pinduoduo:shop:name', async (_, data) => {
    await ViewService.updateViewName(data.viewId, data.shopName)
  })
  ipcMain.handle('pinduoduo:view:duty', async (_, viewId) => {
    const row = await ViewService.getViewDutyStatus(viewId)
    if (row) {
      return row.dataValues.isDuty
    } else {
      return false
    }
  })
  ipcMain.handle('pinduoduo:chat:process', async (_, data) => {
    const view = await ViewService.getViewKnowledge(data.viewId)
    const kbName = view?.knowledge
    const promptName = view?.promptName

    let query = ""
    for (let i = 0; i < data.msgList.length; i++) {
      let item = data.msgList[i]
      if (item.role === "user") {
        query = item.content
      }
    }
    try { 
      const config = getConfig();
      const response = await KbChatApi({
        "view_id": data.viewId,
        "query": query,
        "kb_name": kbName,
        "history": data.msgList,
        "stream": false,
        "model": config.modelName,
        "prompt_name": promptName
      })
      console.log(response);
      
      return response
    } catch (error) {
      log.info(error)
      return null
    }
  })
}
