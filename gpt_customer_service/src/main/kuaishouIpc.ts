import { ipcMain } from 'electron'
import { ViewService } from '../main/services/viewService'
import { KbChatApi } from '../api/chat'
import { getConfig } from '../store/configStore'
import log from 'electron-log'

export function kuaishouIpc(_) {
  // 处理微信小店登录状态
  ipcMain.on('kuaishou:login:status', async (_, data) => {
    await ViewService.updateViewLoginStatus(data.viewId, data.status)
  })

  // 处理微信小店店铺名称
  ipcMain.on('kuaishou:shop:name', async (_, data) => {
    await ViewService.updateViewName(data.viewId, data.shopName)
  })

  // 处理微信小店聊天消息
  ipcMain.handle('kuaishou:chat:process', async (_, data) => {
    const view = await ViewService.getViewKnowledge(data.viewId)
    const kbName = view?.knowledge
    const promptName = view?.promptName

    let query = ''
    for (let i = 0; i < data.msgList.length; i++) {
      let item = data.msgList[i]
      if (item.role === 'user') {
        query = item.content
      }
    }

    try {
      const config = getConfig()
      const response = await KbChatApi({
        view_id: data.viewId,
        query: query,
        kb_name: kbName,
        history: data.msgList,
        stream: false,
        model: config.modelName,
        prompt_name: promptName
      })
      return response
    } catch (error) {
      log.info(error)
      return null
    }
  })

  // 获取视图值班状态
  ipcMain.handle('kuaishou:view:duty', async (_, viewId) => {
    const row = await ViewService.getViewDutyStatus(viewId)
    if (row) {
      return row.dataValues.isDuty
    } else {
      return false
    }
  })
}
