import { sequelize } from '../../database'
import View from '../../models/View'

export class ViewService {
  // 静态方法，用于创建视图
  static async createView(view: {
    name: string
    viewId: string
    shopId: number
    knowledge: string
    userName: string
    password: string
    promptName: string
  }) {
    // 调用View类的create方法，传入view参数，并返回结果
    try {
      return await View.create(view)
    } catch (error) {
      return null
    }
  }

  // 静态方法，用于切换视图的状态
  static async toggleViewDuty(viewId: string) {
    try {
      return await View.update(
        {
          isDuty: sequelize.literal('NOT isDuty')
        },
        {
          // 条件，根据viewId查找视图
          where: {
            viewId: viewId
          }
        }
      )
    } catch (error) {
      return null
    }
  }

  // 静态方法，更新视图登录状态
  static async updateViewLoginStatus(viewId: string, status: boolean) {
    try {
      return await View.update(
        {
          loginState: status
        },
        {
          // 条件，根据viewId查找视图
          where: {
            viewId: viewId
          }
        }
      )
    } catch (error) {
      return null
    }
  }

  // 静态方法，更新视图名称
  static async updateViewName(viewId: string, name: string) {
    try {
      return await View.update(
        {
          name: name
        },
        {
          // 条件，根据viewId查找视图
          where: {
            viewId: viewId
          }
        }
      )
    } catch (error) {
      return null
    }
  }

  // 静态方法，获取视图duuty状态
  static async getViewDutyStatus(viewId: string) {
    try {
      return await View.findOne({
        // 条件，根据viewId查找视图
        where: {
          viewId: viewId
        }
      })
    } catch (error) {
      return null
    }
  }

  // 静态方法，异步删除视图
  static async deleteView(viewId: string) {
    // 更新视图，将isDelet字段设置为true，deletDate字段设置为当前日期
    try {
      return await View.update(
        {
          isDelet: true,
          deletDate: new Date()
        },
        {
          // 条件，根据viewId查找视图
          where: {
            viewId: viewId
          }
        }
      )
    } catch (error) {
      return null
    }
  }

  // 静态方法，异步获取所有视图
  static async listViews() {
    // 使用View模型查找所有视图，并包含Shop模型，返回原始数据，嵌套数据
    try {
      return await View.findAll({
        raw: true,
        nest: true,
        where: {
          isDelet: false
        }
      })
    } catch (error) {
      return null
    }
  }

  // 静态方法，更新视图知识库
  static async updateViewKnowledge(viewId: string, knowledge: string) {
    try {
      return await View.update(
        {
          knowledge: knowledge
        },
        {
          // 条件，根据viewId查找视图
          where: {
            viewId: viewId
          }
        }
      )
    } catch (error) {
      return null
    }
  }

  // 静态方法，更新视图
  static async updateView(
    viewId: string,
    knowledge: string,
    userName: string,
    password: string,
    promptName: string
  ) {
    try {
      return await View.update(
        {
          knowledge: knowledge,
          userName: userName,
          password: password,
          promptName: promptName
        },
        {
          // 条件，根据viewId查找视图
          where: {
            viewId: viewId
          }
        }
      )
    } catch (error) {
      return null
    }
  }

  // 静态方法，获取视图知识库
  static async getViewKnowledge(viewId: string) {
    try {
      return await View.findOne({
        // 返回原始数据，嵌套数据
        raw: true,
        nest: true,
        // 条件，根据viewId查找视图
        where: {
          viewId: viewId
        }
      })
    } catch (error) {
      return null
    }
  }

  // 更新店铺采集状态
  static async updateViewCollectionState(viewId: string, collectionState: number) {
    try {
      return await View.update(
        {
          collectionState: collectionState
        },
        {
          // 条件，根据viewId查找视图
          where: {
            viewId: viewId
          }
        }
      )
    } catch (error) {
      return null
    }
  }
}
