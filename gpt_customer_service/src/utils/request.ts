import axios from 'axios'
import { tansParams } from '../utils/mis'
import log from 'electron-log'

// import router from '@/router'
axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: import.meta.env.VITE_APP_BASE_API,
  // 超时
  timeout: 60 * 1000
})

// request拦截器
service.interceptors.request.use(
  (config) => {
    log.info('请求地址：', config.url)
    log.info('请求参数：', config.params)
    log.info('请求头：', config.headers)
    log.info('请求体：', config.data)

    // get请求映射params参数
    if (config.method === 'get' && config.params) {
      let url = config.url + '?' + tansParams(config.params)
      url = url.slice(0, -1)
      config.params = {}
      config.url = url
    }
    return config
  },
  (error) => {
    log.error(error)
    Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (res) => {
    log.info('响应数据：', res.data)
    log.info('响应状态：', res.status)
    log.info('响应头：', res.headers)
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200
    // 获取错误信息
    const msg = res.data.msg || res.data.data || ''
    // 二进制数据则直接返回
    if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
      return res.data
    }
    if (code === 401) {
      return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
    } else if (code === 500) {
      return Promise.reject(msg)
    } else if (code === 601) {
      return Promise.reject(msg)
    } else if (code !== 200 && code !== 400) {
      return Promise.reject('error')
    } else {
      try {
        const data = JSON.parse(res.data)
        if (data['choices']) {
          if (data['choices'][0]['message']['content'].includes('无法解答')) {
            global.sendWarning('请求人工接入')
          } else if (data['choices'][0]['message']['content'].includes('错误信息')) {
            global.sendWarning(data['choices'][0]['message']['content'])
          }
          return Promise.reject('error')
        } else {
          return Promise.resolve(res.data)
        }
      } catch (error) {
        return Promise.resolve(res.data)
      }
    }
  },
  (error) => {
    let { message } = error
    if (message == 'Network Error') {
      message = '后端接口连接异常'
    } else if (message.includes('timeout')) {
      message = '系统接口请求超时'
    } else if (message.includes('Request failed with status code')) {
      message = '系统接口' + message.substr(message.length - 3) + '异常'
    }
    if (error.response && error.response.status === 422) {
      log.error('请求错误：', JSON.stringify(error.response.data))
      return Promise.reject(error)
    }
    return Promise.reject(error)
  }
)

export default service
