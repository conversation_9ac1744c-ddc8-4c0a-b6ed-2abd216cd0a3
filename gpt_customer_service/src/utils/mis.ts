export function tansParams(params: { [x: string]: any }) {
  let result = ''
  for (const propName of Object.keys(params)) {
    const value = params[propName]
    if (value !== null && typeof value !== 'undefined') {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && typeof value[key] !== 'undefined') {
            var subPart = encodeURIComponent(key) + '=' + encodeURIComponent(value[key]) + '&'
            result += subPart
          }
        }
      }
    }
  }
  return result.substring(0, result.length - 1)
}