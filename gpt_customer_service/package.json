{"name": "gpt_customer_service", "version": "1.0.0", "description": "An Electron application with Vue and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev --watch", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@element-plus/icons-vue": "^2.3.1", "@types/sequelize": "^4.28.20", "axios": "^1.10.0", "electron-conf": "^1.3.0", "electron-context-menu": "^4.1.0", "electron-log": "^5.4.1", "electron-updater": "^6.3.9", "element-plus": "^2.9.10", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7", "sqlite3": "^5.1.7", "uuid": "^11.1.0", "vue-router": "^4.5.1"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/axios": "^0.9.36", "@types/node": "^22.14.1", "@vitejs/plugin-vue": "^5.2.4", "electron": "^35.1.5", "electron-builder": "^25.1.8", "electron-vite": "^3.1.0", "eslint": "^9.24.0", "eslint-plugin-vue": "^10.0.0", "prettier": "^3.5.3", "sass": "^1.89.0", "sass-loader": "^16.0.5", "sequelize-auto-migrations": "^1.0.3", "typescript": "^5.8.3", "vite": "^6.2.6", "vue": "^3.5.13", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^2.2.8"}}