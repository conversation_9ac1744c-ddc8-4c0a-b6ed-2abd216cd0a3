# gpt_customer_service

An Electron application with Vue and TypeScript

## Recommended IDE Setup

- [VSCode](https://code.visualstudio.com/) + [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) + [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar)

## Project Setup

### Install

```bash
$ npm install
```

### Development

```bash
$ npm run dev
```

### Build

```bash
# For windows
$ npm run build:win

# For macOS
$ npm run build:mac

# For Linux
$ npm run build:linux
```

# 项目结构说明

## 根目录文件

- &zwnj;**.editorconfig**&zwnj;：配置文件，用于定义项目的编码风格，如缩进、换行符等，确保不同编辑器间的代码格式一致。
- &zwnj;**.env.development**&zwnj; 和 &zwnj;**.env.production**&zwnj;：环境变量文件，分别用于开发和生产环境，存储敏感信息或配置选项。
- &zwnj;**.npmrc**&zwnj;：npm配置文件，用于定义npm的行为，如镜像源、版本管理等。
- &zwnj;**.prettierignore**&zwnj;：Prettier忽略文件，指定哪些文件或目录不应被Prettier格式化。
- &zwnj;**.prettierrc.yaml**&zwnj;：Prettier配置文件，定义代码格式化规则。
- &zwnj;**package-lock.json**&zwnj;：npm生成的依赖锁文件，确保项目依赖的版本一致性。
- &zwnj;**package.json**&zwnj;：项目的npm配置文件，包含项目信息、依赖、脚本等。
- &zwnj;**README.md**&zwnj;：项目说明文档，介绍项目功能、使用方式等。
- &zwnj;**vite-env.d.ts**&zwnj;：Vite环境变量类型定义文件，用于TypeScript项目中。

## 构建与配置相关

- &zwnj;**build**&zwnj;：构建输出目录，存放打包后的应用文件。
  - &zwnj;**entitlements.mac.plist**&zwnj;（macOS专用）：定义macOS应用的权限和沙盒设置。
  - &zwnj;**icon.icns**&zwnj;（macOS专用）、&zwnj;**icon.ico**&zwnj;（Windows专用）、&zwnj;**icon.png**&zwnj;：应用图标文件。
- &zwnj;**dev-app-update.yml**&zwnj;：Electron Builder的自动更新配置文件。
- &zwnj;**electron-builder.yml**&zwnj;：Electron Builder的配置文件，用于定义打包和发布的选项。
- &zwnj;**electron.vite.config.ts**&zwnj;：Electron + Vite的配置文件，继承Vite的所有优点并针对Electron进行预先配置。
- &zwnj;**tsconfig.json**&zwnj;、&zwnj;**tsconfig.node.json**&zwnj;、&zwnj;**tsconfig.web.json**&zwnj;：TypeScript配置文件，分别用于通用、Node.js和Web环境的编译设置。

## 开发工具与测试

- &zwnj;**eslint.config.mjs**&zwnj;：ESLint配置文件，定义代码检查规则。

## 资源文件

- &zwnj;**resources**&zwnj;：存放应用的额外资源文件，如图标等。

## 源代码

- &zwnj;**src**&zwnj;：源代码目录。
  - &zwnj;**api**&zwnj;：API接口文件，用于与外部服务通信。
  - &zwnj;**database.ts**&zwnj;：数据库操作文件。
  - &zwnj;**global.d.ts**&zwnj;：全局类型定义文件。
  - &zwnj;**main**&zwnj;：Electron主进程代码目录。
    - &zwnj;**douyinIpc.ts**&zwnj;、&zwnj;**testIpc.ts**&zwnj;等：IPC（进程间通信）处理文件。
    - &zwnj;**index.ts**&zwnj;：主进程入口文件。
    - &zwnj;**services**&zwnj;：服务文件，封装业务逻辑。
  - &zwnj;**models**&zwnj;：数据模型文件，定义应用数据结构。
  - &zwnj;**platform_scripts**&zwnj;：平台特定脚本文件。
  - &zwnj;**preload**&zwnj;：预加载脚本目录，用于在渲染器进程加载前执行代码。
    - &zwnj;**index.ts**&zwnj;：预加载脚本入口文件。
  - &zwnj;**renderer**&zwnj;：渲染器进程代码目录（前端代码）。
    - &zwnj;**index.html**&zwnj;：HTML模板文件。
    - &zwnj;**src**&zwnj;：前端源代码目录。
      - &zwnj;**App.vue**&zwnj;：Vue组件根文件。
      - &zwnj;**assets**&zwnj;：静态资源目录。
      - &zwnj;**components**&zwnj;：Vue组件目录。
      - &zwnj;**views**&zwnj;：视图目录，存放页面组件。
      - &zwnj;**main.ts**&zwnj;：前端入口文件。
  - &zwnj;**utils**&zwnj;：工具函数目录。

