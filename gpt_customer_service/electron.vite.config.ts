import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import vue from '@vitejs/plugin-vue'
import fs from 'fs'
import path from 'path'

const platformEntries = {}
const scriptsDir = path.resolve('src/platform_scripts')

// 遍历目录生成入口配置
fs.readdirSync(scriptsDir).forEach((file) => {
  if (file.endsWith('.ts')) {
    const name = path.basename(file, '.ts')
    platformEntries[name] = path.resolve(scriptsDir, file)
  }
})

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()],
    build: {
      outDir: 'out/main', // 显式指定输出目录
      rollupOptions: {
        input: resolve('src/main/index.ts'), // 显式指定入口文件
        external: ['sqlite3']
      }
    }
  },
  preload: {
    plugins: [externalizeDepsPlugin()],
    build: {
      rollupOptions: {
        input: {
          index: resolve('src/preload/index.ts'),
          ...platformEntries
        },
        output: {
          entryFileNames: (chunkInfo) => {
            return chunkInfo.facadeModuleId?.includes('platform_scripts')
              ? 'platform_scripts/[name].js'
              : '[name].js'
          },
          format: 'cjs'
        }
      }
    }
  },
  renderer: {
    resolve: {
      alias: {
        '@renderer': resolve('src/renderer/src')
      }
    },
    plugins: [vue()]
  }
})
